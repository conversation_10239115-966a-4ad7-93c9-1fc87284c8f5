export interface userDashboardType {
    user: {
    id: string
    firstname: string
    lastname: string
    email: string
    position: string
    lastLogin: Date
    status: "active" | "inactive"
    role: "student" | "lecturer" | "admin"
    password: string
    profileImage?: string
    }

    courses: {
        id: string
        name: string
        description: string
        coverImage: string
        time: number
        level: string
        countLesson : number
        certify: boolean

    }
}