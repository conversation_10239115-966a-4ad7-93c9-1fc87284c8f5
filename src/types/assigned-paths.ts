// Type definitions สำหรับการมอบหมายเส้นทางการเรียนรู้

export interface AssignedPath {
  studentId: string
  pathId: string // ID ของ Learning Path
  userId: string // รายการ User ID ที่เป็น student คั่นด้วยเครื่องหมายจุลภาค เช่น "user-017,user-018"
}

// Type สำหรับการจัดการข้อมูล
export interface AssignedPathOperations {
  getAssignedPathsByUserId: (userId: string) => AssignedPath[]
  getAssignedPathsByPathId: (pathId: string) => AssignedPath | undefined
  getUsersAssignedToPath: (pathId: string) => string[]
  getPathsAssignedToUser: (userId: string) => string[]
  isUserAssignedToPath: (userId: string, pathId: string) => boolean
  addUserToPath: (pathId: string, userId: string) => boolean
  removeUserFromPath: (pathId: string, userId: string) => boolean
  updateUsersForPath: (pathId: string, userIds: string[]) => void
}

// Type สำหรับ UI Components
export interface AssignedPathWithDetails {
  pathId: string
  pathName: string
  pathDescription: string
  userIds: string[]
  userCount: number
}

export interface StudentWithPaths {
  userId: string
  userFirstname: string
  userLastname: string
  userEmail: string
  userPosition: string
  userStatus: string
  userProfileImage?: string
  assignedPaths: {
    pathId: string
    pathName: string
  }[]
}
