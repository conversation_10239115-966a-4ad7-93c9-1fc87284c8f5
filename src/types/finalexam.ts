
import type { Question } from "./quizzes"

// โครงสร้างข้อมูลข้อสอบท้ายบท
export interface FinalExam {
  id: string
  title: string
  description?: string
  courseId: string
  questions: Question[]
  passingScore: number
  timeLimit?: number
}

// โครงสร้างข้อมูลผลลัพธ์การทำข้อสอบท้ายบท
export interface ExamResult {
  examId: string
  userId: string
  score: number
  passed: boolean
  completedAt: Date
  answers: {
    questionId: string
    selectedChoiceId: string
    isCorrect: boolean
  }[]
}
