
export interface Choice {
    id: string
    text: string
    isCorrect: boolean
  }
  
  // โครงสร้างข้อมูลคำถาม
  export interface Question {
    id: string
    question: string
    choices: Choice[]
    explanation?: string
    imageUrl?: string
  }
  
  // โครงสร้างข้อมูลควิซ
  export interface Quiz {
    id: string
    title: string
    description?: string
    questions: Question[]
    courseId?: string
    lessonId?: string
    contentId?: string
    passingScore?: number
  }
  
  // โครงสร้างข้อมูลผลลัพธ์การทำควิซ
  export interface QuizResult {
    quizId: string
    userId: string
    score: number
    passed: boolean
    completedAt: Date
    answers: {
      questionId: string
      selectedChoiceId: string
      isCorrect: boolean
      
    }[]
  }
  