import { api } from '@/lib/api'

// Types for API responses
export interface User {
  id: number
  user_picture: string
  user_fname: string
  user_lname: string
  user_email: string
  user_position: string
  user_login_date: string
  user_status: string
  user_role: string
  user_slug: string
}

export interface CreateCourseRequest {
  title: string
  lecturer: string // lecturer slug
  description: string
  moduleDescription: string
  difficulty: string
  duration: number // hours
  status: string
  coverImage?: string | null
  certify: boolean
  lessons: Lesson[]
}

export interface Lesson {
  id?: string
  name: string
  description: string
  time: number
  content: Content[]
}

export interface Content {
  id?: string
  name: string
  typecontent: string
  details: string
  time: number
}

export interface CreateCourseResponse {
  message: string
  courseId?: string
}

// Course API service
export const courseService = {
  // Create a new course
  async createCourse(courseData: CreateCourseRequest): Promise<CreateCourseResponse> {
    try {
      const response = await api.post<CreateCourseResponse>('/courses', courseData)
      return response
    } catch (error) {
      console.error('Failed to create course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to create course')
    }
  },

  // Get all courses
  async getCourses(): Promise<any[]> {
    try {
      const response = await api.get<any[]>('/courses')
      return response
    } catch (error) {
      console.error('Failed to fetch courses:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch courses')
    }
  },

  // Get course by ID
  async getCourseById(courseId: string): Promise<any> {
    try {
      const response = await api.get<any>(`/courses/${courseId}`)
      return response
    } catch (error) {
      console.error('Failed to fetch course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch course')
    }
  },

  // Delete course by slug
  async deleteCourse(slug: string): Promise<any> {
    try {
      const response = await api.delete<any>(`/courses/${slug}`)
      return response
    } catch (error) {
      console.error('Failed to delete course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to delete course')
    }
  }
}

// User API service for fetching lecturers
export const userService = {
  // Get all users (to filter lecturers)
  async getUsers(): Promise<User[]> {
    try {
      const response = await api.get<User[]>('/users')
      return response
    } catch (error) {
      console.error('Failed to fetch users:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch users')
    }
  },

  // Get lecturers only
  async getLecturers(): Promise<User[]> {
    try {
      const users = await this.getUsers()
      return users.filter(user => user.user_role === 'lecturer')
    } catch (error) {
      console.error('Failed to fetch lecturers:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch lecturers')
    }
  }
}
