"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { LockKeyhole } from "lucide-react"

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("")
  const [error, setError] = useState("")
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!email) {
      setError("กรุณากรอกอีเมล")
      return
    }

    // Here you would typically call an API to send a reset email
    console.log("Sending reset email to:", email)

    // For demo purposes, we'll just navigate to the OTP verification page
    router.push(`/verify-otp?email=${encodeURIComponent(email)}`)
  }

  return (
    <>
      {/* Background Image - SypheatBuilding */}
      <div className="min-h-screen relative">
        <div className="absolute inset-0 z-0">
          <Image
            src="/e-med/img/SynphaetBuilding.jpg"
            alt="Sypheat Building Background"
            fill
            className="object-cover "
            priority
          />
          {/* Dark overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20" />
        </div>

        {/* Content */}
        <div className="relative z-10 min-h-screen flex justify-center items-center">
          {/* Main Content */}
          <div className="w-full max-w-4xl">
            <div className="backdrop-blur-[2px] border border-white  rounded-2xl lg:w-[100vh] lg:h-[60vh] ipad-pro:h-[40vh] ipad-pro:w-[65vh] overflow-hidden shadow-2xl flex flex-col md:flex-row">
              {/* Left Side - Background Image */}
              <div className="relative hidden md:block w-1/2">
                <div className="absolute inset-0 backdrop-blur  z-10" />

                <div className="absolute top-16 mx-28 -left-16 text- p-4 z-20">
                  <h1 className="bg-gradient-to-r  from-[#2e8574] to-[#293D97] leading-snug text-5xl font-extrabold bg-clip-text text-transparent">
                    E-MED LEARNING
                  </h1>
                </div>
              </div>

              {/* Right Side - Forgot Password Form */}
              <div className="w-full md:w-1/2 p-8 flex flex-col justify-center bg-white border-l border-white/20">
                <h2 className="text-3xl font-bold text-center mt-10 mb-2 text-gray-800">รีเซ็ทรหัสผ่าน</h2>

                <div className="flex justify-center mb-6">
                  <div className="bg-blue-100/30 backdrop-blur-sm p-6 rounded-full border border-white/20">
                    <LockKeyhole size={80} className="text-[#3B4A8F]" />
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700  mb-1">
                      อีเมล
                    </label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 bg-white/30 backdrop-blur-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1] text-gray-800 placeholder-gray-600"
                    />
                    {error && <p className="text-red-600 text-xs mt-1">{error}</p>}
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-[#3B4A8F] hover:bg-[#3B4A8F]/90 text-white font-bold text-lg py-2 px-4 rounded-md shadow-lg"
                  >
                    ส่งอีเมลยืนยัน
                  </button>
                </form>

                <div className="text-center mt-4">
                  <Link href="/login" className="font-medium text-[#3B4A8F] hover:text-[#3B4A8F]/80">
                    กลับไปหน้าเข้าสู่ระบบ
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
