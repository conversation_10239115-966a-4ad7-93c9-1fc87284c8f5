"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Navbar from "@/components/headers"
import { useRouter, useSearchParams } from "next/navigation"

export default function ResetPasswordPage() {
  const searchParams = useSearchParams()
  const email = searchParams.get("email") || ""

  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  })

  const [errors, setErrors] = useState({
    password: "",
    confirmPassword: "",
  })

  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    const newErrors: any = {}
    if (!formData.password) newErrors.password = "กรุณากรอกรหัสผ่าน"
    if (formData.password.length < 8) newErrors.password = "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"
    if (!formData.confirmPassword) newErrors.confirmPassword = "กรุณายืนยันรหัสผ่าน"
    if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "รหัสผ่านไม่ตรงกัน"

    setErrors(newErrors)

    // If no errors, proceed with password reset
    if (Object.keys(newErrors).length === 0) {
      console.log("Resetting password for:", email)
      console.log("New password:", formData.password)

      // Here you would typically call an API to reset the password

      // For demo purposes, we'll just navigate to the login page
      router.push("/login")
    }
  }

  return (
    <>
      <Navbar />
      {/* Background Image - SypheatBuilding */}
      <div className="min-h-screen relative">
        <div className="absolute inset-0 z-0">
          <Image
            src="/e-med/img/SynphaetBuilding.jpg"
            alt="Sypheat Building Background"
            fill
            className="object-cover"
            priority
          />
          {/* Dark overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20" />
        </div>

        {/* Content */}
        <div className="relative z-10 min-h-screen flex justify-center items-center">
          {/* Main Content */}
          <div className="w-full max-w-4xl">
            <div className="backdrop-blur-[2px]  border border-white ipad-pro:h-[40vh] ipad-pro:w-[65vh] rounded-2xl lg:w-[100vh] h-[50vh] lg:h-[60vh] overflow-hidden shadow-2xl flex flex-col md:flex-row">
              {/* Left Side - Background Image */}
              <div className="relative hidden md:block w-1/2">
                <div className="absolute inset-0 backdrop-blur bg-white/5 z-10" />

                <div className="absolute top-16 mx-28 -left-16 text- p-4 z-20">
                  <h1 className="bg-gradient-to-r from-[#2e8574] to-[#293D97] leading-snug text-5xl font-extrabold bg-clip-text text-transparent">
                    E-MED LEARNING
                  </h1>
                </div>
              </div>

              {/* Right Side - Reset Password Form */}
              <div className="w-full md:w-1/2 p-8 flex flex-col justify-between h-full  bg-white border-l border-white/20">
                <div>
                  <h2 className="text-3xl font-bold text-center mt-10 mb-2 text-gray-800">รีเซ็ทรหัสผ่าน</h2>
                  <p className="text-center text-sm text-gray-700 mb-6">กรอกรหัสผ่านใหม่เพื่อการเปลี่ยนรหัสผ่านของคุณ</p>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        รหัสผ่าน
                      </label>
                      <input
                        id="password"
                        name="password"
                        type="password"
                        required
                        value={formData.password}
                        onChange={handleChange}
                        className="w-full px-3 py-2 bg-gray-100 backdrop-blur-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1] text-gray-800 placeholder-gray-600"
                      />
                      {errors.password && <p className="text-red-600 text-xs mt-1">{errors.password}</p>}
                    </div>

                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        ยืนยันรหัสผ่าน
                      </label>
                      <input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        required
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        className="w-full px-3 py-2 bg-gray-100 backdrop-blur-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1] text-gray-800 placeholder-gray-600"
                      />
                      {errors.confirmPassword && <p className="text-red-600 text-xs mt-1">{errors.confirmPassword}</p>}
                    </div>
                  </form>
                </div>

                <button
                  type="submit"
                  className="w-full bg-[#3B4A8F] hover:bg-[#3B4A8F]/90 text-white font-bold text-lg py-2 px-4 rounded-md mt-auto shadow-lg"
                  onClick={handleSubmit}
                >
                  รีเซ็ทรหัสผ่าน
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
