@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai:wght@100;200;300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

/* สำหรับ iPad Mini ในแนวนอน */
@media (min-width: 1024px) and (max-width: 1199px) and (orientation: landscape) {
  .ipad-mini\:w-\[48vh\] {
    width: 48vh; /* ปรับขนาดตามที่ต้องการ */
  }
}

/* สำหรับ iPad Air ในแนวนอน */
@media (min-width: 1180px) and (max-width: 1280px) and (orientation: landscape) {
  .ipad-air\:w-\[50vh\] {
    width: 50vh; /* ปรับขนาดตามที่ต้องการ */
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */
@font-face {
  font-family: 'Noto Sans Thai';
  src: url('../assets/fonts/NotoSansThai-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-thumb {
  background-color: #8a8a8a;
  border-radius: 10px;
}

.ibm-plex-sans-thai-thin {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 100;
  font-style: normal;
}

.ibm-plex-sans-thai-extralight {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 200;
  font-style: normal;
}

.ibm-plex-sans-thai-light {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.ibm-plex-sans-thai-regular {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.ibm-plex-sans-thai-medium {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.ibm-plex-sans-thai-semibold {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 600;
  font-style: normal;
}

.ibm-plex-sans-thai-bold {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 700;
  font-style: normal;
}

@layer components {
  input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
  }

  input[type="checkbox"]:checked {
    background-color: #008268;
    border-color: #008268;
  }

  input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    top: 2px;
    left: 5px;
    width: 5px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  input[type="checkbox"]:focus {
    outline: none;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .hide-scrollbar::-webkit-scrollbar {
  display: none;
  }
  .hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
  }

    /* Hide scrollbar in iframe */
  .pdf-iframe {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .pdf-iframe::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* Also hide scrollbar in modal container */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
  }
}
