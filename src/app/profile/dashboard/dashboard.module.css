/* Dashboard Container - ครอบทั้งหน้า */
.dashboardContainer {
    min-height: 100vh;
    background-color: #f9fafb;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 0; /* ลบ padding ด้านบน */
    max-width: 100%; /* เพิ่มเพื่อให้แน่ใจว่าไม่เกินความกว้างของหน้าจอ */
  }
  
  /* Dashboard Layout - จัดวาง sidebar และ content */
  .dashboardLayout {
    display: flex;
    flex: 1;
    position: relative;
    height: 100%;
    width: 100%;
    max-width: 100%; /* เพิ่มเพื่อให้แน่ใจว่าไม่เกินความกว้างของหน้าจอ */
  }
  
  /* Sidebar */
  .sidebar {
    width: 60px; /* ลดขนาดให้เล็กลงอีก */
    position: fixed;
    height: 100vh;
    background-color: #008268;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 0;
    z-index: 30; /* เพิ่ม z-index ให้อยู่เหนือ content แต่ต่ำกว่า navbar */
    top: 0; /* เริ่มจากด้านบนสุด */
  }
  
  /* ลบเฉพาะส่วนโลโก้บนสุดของ sidebar */
  .sidebarLogo {
    display: none; /* เปลี่ยนจาก margin-bottom: 2.5rem เป็น display: none */
  }
  
  .logoCircle {
    width: 40px;
    height: 40px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .sidebarNav {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    flex-grow: 1;
  }
  
  .sidebarNavItem {
    color: white;
    padding: 0.75rem;
    border-radius: 0.75rem;
    transition: background-color 0.2s;
  }
  
  .sidebarNavItem:hover {
    background-color: #006e58;
  }
  
  .sidebarNavItemActive {
    color: white;
    padding: 0.75rem;
    border-radius: 0.75rem;
    background-color: #006e58;
  }
  
  /* ลบส่วน sidebarFooter และ userAvatar ออก */
  .sidebarFooter {
    display: none;
  }
  
  .userAvatar,
  .userAvatarPlaceholder {
    display: none;
  }
  
  /* Main Content */
  .mainContent {
    flex: 1;
    margin-left: 60px; /* ปรับให้ตรงกับความกว้างของ sidebar */
    width: calc(100% - 60px);
    padding-top: 64px; /* เพิ่ม padding-top เท่ากับความสูงของ navbar */
    max-width: 100%; /* เพิ่มเพื่อให้แน่ใจว่าไม่เกินความกว้างของหน้าจอ */
  }
  
  .contentWrapper {
    max-width: 1280px;
    margin: 0 auto;
    padding: 1.5rem;
    width: 100%;
    width: 100%;
    max-width: 100%;
    margin: 0;
  }
  
  /* Dashboard Header */
  .dashboardHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .welcomeTitle {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
  }
  
  .welcomeSubtitle {
    color: #6b7280;
  }
  
  .headerActions {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .searchContainer {
    position: relative;
  }
  
  .searchIcon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
  }
  
  .searchInput {
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 9999px;
    width: 16rem;
    outline: none;
  }
  
  .searchInput:focus {
    border-color: #008268;
    box-shadow: 0 0 0 2px rgba(0, 130, 104, 0.2);
  }
  
  .notificationButton {
    position: relative;
    padding: 0.5rem;
    color: #6b7280;
    transition: color 0.2s;
  }
  
  .notificationButton:hover {
    color: #008268;
  }
  
  .notificationBadge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background-color: #ef4444;
    border-radius: 50%;
  }
  
  /* Dashboard Grid */
  .dashboardGrid {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 1.5rem;
  }
  
  /* Profile Card */
  .profileCard {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 5rem; /* เพิ่มระยะห่างจากด้านบนเพื่อไม่ให้ถูก navbar บัง */
  }
  
  /* ปรับ padding ของ profileCardContent กลับมาเป็นค่าเดิม */
  .profileCardContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: initial; /* ลบ padding-top: 0 */
  }
  
  /* นำรูปโปรไฟล์ในส่วน profile card กลับมา */
  .profileImageContainer {
    width: 6rem;
    height: 6rem;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #e6f2f0;
    margin-bottom: 1rem;
    display: block; /* เปลี่ยนจาก display: none เป็น display: block */
  }
  
  .profileImagePlaceholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom right, #004c41, #2fbcc1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
  
  .profileName {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    text-align: center;
  }
  
  .profilePosition {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .statsGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    width: 100%;
    margin-top: 0.5rem;
  }
  
  .statCard {
    background-color: #f9fafb;
    padding: 0.75rem;
    border-radius: 0.5rem;
    text-align: center;
  }
  
  .statValue {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
  }
  
  .statLabel {
    font-size: 0.75rem;
    color: #6b7280;
  }
  
  .recommendedSection {
    margin-top: 1.5rem;
  }
  
  .sectionTitle {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    margin-bottom: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .viewAllLink {
    font-size: 0.75rem;
    color: #008268;
  }
  
  .viewAllLink:hover {
    text-decoration: underline;
  }
  
  .recommendedList {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .recommendedItem {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }
  
  .recommendedItem:hover {
    background-color: #f3f4f6;
  }
  
  .recommendedItemIcon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background-color: #e6f2f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: #008268;
  }
  
  .recommendedItemContent {
    flex: 1;
    min-width: 0;
  }
  
  .recommendedItemTitle {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .recommendedItemMeta {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }
  
  .recommendedItemArrow {
    color: #9ca3af;
  }
  
  .emptyState {
    text-align: center;
    padding: 1rem;
    color: #6b7280;
    font-size: 0.875rem;
  }
  
  /* Content Area */
  .contentArea {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  /* Featured Banner */
  .featuredBanner {
    background: linear-gradient(to right, #008268, #2fbcc1);
    border-radius: 0.75rem;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
    height: 180px;
    display: flex;
    align-items: center;
  }
  
  .bannerBackground {
    position: absolute;
    right: 0;
    top: 0;
    width: 10rem;
    height: 10rem;
    opacity: 0.2;
  }
  
  .bannerContent {
    position: relative;
    z-index: 10;
  }
  
  .bannerTitle {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }
  
  .bannerDescription {
    margin-bottom: 1rem;
    max-width: 32rem;
  }
  
  .bannerButton {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: white;
    color: #008268;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.2s;
  }
  
  .bannerButton:hover {
    background-color: #f9fafb;
  }
  
  /* เพิ่ม spacer แทนส่วน header ที่ลบไป */
  .spacer {
    margin-bottom: 1.5rem;
  }
  
  /* Courses Section */
  .coursesSection {
    margin-bottom: 2rem;
  }
  
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .sectionTitle {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
  }
  
  /* ปรับขนาดการ์ดคอร์สให้สมส่วน */
  .courseCard {
    background-color: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
    max-width: 100%;
    max-height: 360px; /* กำหนดความสูงสูงสุด */
  }
  
  .courseImageContainer {
    position: relative;
    height: 9rem;
    min-height: 9rem; /* กำหนดความสูงขั้นต่ำ */
    max-height: 9rem; /* กำหนดความสูงสูงสุด */
  }
  
  .courseStatus {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
  }
  
  .statusBadge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9999px;
  }
  
  .statusCompleted {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  .statusInProgress {
    background-color: #dbeafe;
    color: #1e40af;
  }
  
  .statusNotStarted {
    background-color: #f3f4f6;
    color: #4b5563;
  }
  
  .courseContent {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden; /* ป้องกันเนื้อหาล้น */
  }
  
  .courseTitle {
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  
  .courseTeacher {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }
  
  .courseProgress {
    margin-bottom: 0.5rem;
  }
  
  .progressHeader {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
  }
  
  .progressBar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
  }
  
  .progressFill {
    height: 100%;
    background-color: #008268;
    border-radius: 9999px;
  }
  
  .courseMeta {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.75rem;
  }
  
  .metaDivider {
    margin: 0 0.5rem;
  }
  
  .courseButton {
    display: block;
    width: 100%;
    text-align: center;
    background-color: #008268;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: background-color 0.2s;
    margin-top: auto;
  }
  
  .courseButton:hover {
    background-color: #006e58;
  }
  
  .emptyCoursesState {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
  }
  
  .emptyStateText {
    color: #6b7280;
    margin-bottom: 1rem;
  }
  
  .emptyStateButton {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #008268;
    color: white;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
  }
  
  .emptyStateButton:hover {
    background-color: #006e58;
  }
  
  /* Instructors Section */
  .instructorsSection {
    margin-bottom: 2rem;
  }
  
  .instructorsGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .instructorCard {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s;
  }
  
  .instructorCard:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .instructorAvatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
  }
  
  .instructorAvatarPlaceholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom right, #004c41, #2fbcc1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
  
  .instructorInfo {
    flex: 1;
  }
  
  .instructorName {
    font-weight: 500;
    color: #1f2937;
  }
  
  .instructorPosition {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  .instructorArrow {
    color: #9ca3af;
  }
  
  /* ปรับ grid ให้สมส่วนมากขึ้น */
  .coursesGrid {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
  }
  
  /* Responsive Adjustments */
  @media (max-width: 1280px) {
    .coursesGrid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  @media (max-width: 1024px) {
    .dashboardGrid {
      grid-template-columns: 1fr;
    }
  
    .profileCard {
      position: static;
      margin-bottom: 1.5rem;
    }
  
    /* ปรับ responsive */
    .coursesGrid {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  @media (max-width: 768px) {
    /* ปรับ responsive */
    .coursesGrid {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  
    .coursesGrid {
      grid-template-columns: 1fr;
    }
  
    .instructorsGrid {
      grid-template-columns: 1fr;
    }
  
    .dashboardHeader {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  
    .headerActions {
      width: 100%;
    }
  
    .searchContainer {
      width: 100%;
    }
  
    .searchInput {
      width: 100%;
    }
  }
  
  @media (max-width: 640px) {
    .sidebar {
      width: 50px;
    }
  
    .mainContent {
      margin-left: 50px;
      width: calc(100% - 50px);
    }
  
    .contentWrapper {
      padding: 1rem;
    }
  }
  