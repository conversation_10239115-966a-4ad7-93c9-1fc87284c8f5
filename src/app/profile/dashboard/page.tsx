"use client"

import { useEffect, useState, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  BookOpen,
  Award,
  Calendar,
  ChevronRight,
  Clock,
  ChevronLeft,
  ChevronDown,
  Filter,
  CheckCircle,
  Circle,
  Target,
} from "lucide-react"
import { getMedicalUsersData } from "@/data/allUsers"
import { getUserProgress } from "@/data/userProgress"
import { getCoursesData } from "@/data/allCourses"
import Navbar from "@/components/headers"
import CourseCard from "@/components/progressCard"
import type { MedicalUser } from "@/types/users"
import type { CourseWithProgress } from "@/types/extendedCourses"
import type { LearningStatus } from "@/types/progress"
import DashboardSidebar from "@/components/DashboardSidebar"
import PathPreviewCard from "@/components/PathPreviewCard"

// ประเภทของการกรองคอร์ส
type FilterType = "all" | "completed" | "in_progress"

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<MedicalUser | null>(null)
  const [userCourses, setUserCourses] = useState<CourseWithProgress[]>([])
  const [filteredCourses, setFilteredCourses] = useState<CourseWithProgress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentFilter, setCurrentFilter] = useState<FilterType>("all")
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const coursesScrollRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // ตรวจสอบว่ามีการ login หรือไม่
    const userId = localStorage.getItem("userId")
    if (!userId) {
      router.push("/login")
      return
    }

    // ดึงข้อมูลผู้ใช้
    const userData = getMedicalUsersData.find((user) => user.id === userId)
    if (userData) {
      setUser(userData)

      // ดึงข้อมูลความก้าวหน้าในการเรียน
      const progress = getUserProgress(userId)

      // รวมข้อมูล progress เข้ากับข้อมูลคอร์ส
      const coursesWithProgress = getCoursesData.map((course) => {
        const userProgress = progress.find((p) => p.courseId === course.id)
        if (userProgress) {
          return {
            ...course,
            LearningStatus: userProgress.status as LearningStatus,
            progress: userProgress.progress,
            completedLessons: userProgress.completedLessons,
            totalLessons: userProgress.totalLessons,
          }
        }
        return course
      })

      // แยกคอร์สที่ลงทะเบียนแล้วและยังไม่ได้ลงทะเบียน
      const enrolledCourses = coursesWithProgress.filter(
        (course) => course.LearningStatus === "completed" || course.LearningStatus === "in_progress",
      )

      setUserCourses(enrolledCourses)
      setFilteredCourses(enrolledCourses)
    }

    setIsLoading(false)
  }, [router])

  // กรองคอร์สตามสถานะ
  useEffect(() => {
    if (currentFilter === "all") {
      setFilteredCourses(userCourses)
    } else {
      setFilteredCourses(userCourses.filter((course) => course.LearningStatus === currentFilter))
    }
  }, [currentFilter, userCourses])

  // คำนวณจำนวนคอร์สที่เรียนจบแล้ว
  const completedCourses = userCourses.filter((course) => course.LearningStatus === "completed").length

  // ฟังก์ชันเลื่อนการ์ดคอร์ส
  const scrollCourses = (direction: "left" | "right") => {
    if (coursesScrollRef.current) {
      const scrollAmount = 300 // ปรับตามความเหมาะสม
      if (direction === "left") {
        coursesScrollRef.current.scrollBy({ left: -scrollAmount, behavior: "smooth" })
      } else {
        coursesScrollRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" })
      }
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">ไม่พบข้อมูลผู้ใช้</h1>
          <p className="text-gray-600 mb-6">กรุณาเข้าสู่ระบบเพื่อดูข้อมูลแดชบอร์ด</p>
          <button
            onClick={() => router.push("/login")}
            className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
          >
            เข้าสู่ระบบ
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f9fafb] w-full">
        <div className="flex w-full">
          {/* Sidebar */}
      <DashboardSidebar />

          {/* Main Content - ปรับให้เต็มหน้าจอ */}
          <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
            <div className="w-full p-6">
              {/* Spacer instead of header */}
              <div className="mb-6"></div>

              {/* Main Dashboard Content */}
              <div className="grid grid-cols-1 lg:grid-cols-[1fr_3fr] gap-6 w-full">
                {/* User Profile Card */}
                <div className="bg-white rounded-xl shadow-sm p-6 h-fit lg:sticky lg:top-20">
                  <div className="flex flex-col items-center">
                    <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-[#e6f2f0] mb-4">
                      {user?.profileImage ? (
                        <Image
                          src={user.profileImage || "/placeholder.svg"}
                          alt={user.firstname}
                          width={96}
                          height={96}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-[#004c41] to-[#2fbcc1] flex items-center justify-center text-white">
                          <span className="text-2xl font-medium">
                            {user?.firstname.charAt(0)}
                            {user?.lastname.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <h2 className="text-xl font-bold text-gray-800 text-center">
                      {user?.firstname} {user?.lastname}
                    </h2>
                    <p className="text-sm text-gray-600 mb-4 text-center">{user?.position}</p>

                    <div className="grid grid-cols-2 gap-4 w-full mt-2">
                      <div className="bg-gray-50 p-3 rounded-lg text-center">
                        <div className="text-2xl font-bold text-gray-800">{userCourses.length}</div>
                        <div className="text-xs text-gray-500">คอร์สเรียน</div>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-lg text-center">
                        <div className="text-2xl font-bold text-gray-800">{completedCourses}</div>
                        <div className="text-xs text-gray-500">ใบรับรอง</div>
                      </div>
                    </div>
                  </div>

                  {/* Pathways  Courses Section */}

                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-gray-700 mb-3 flex justify-between items-center">
                      <span>เส้นทางการเรียนของคุณ</span>
                      <Link href="/profile/certificate-pathways" className="text-xs text-[#008268] hover:underline">
                        ดูทั้งหมด
                      </Link>
                    </h3>
                    <div className="h-[500px] overflow-y-auto hide-scrollbar">
                      <PathPreviewCard userId={user?.id} />
                    </div>

                  </div>
                </div>

                {/* Pathway ends */}

                {/* Main Content Area */}
                <div className="flex flex-col gap-6 w-full">
                  {/* Featured Course Banner */}
                  <div className="bg-gradient-to-r from-[#008268] to-[#2fbcc1] rounded-xl p-6 text-white relative overflow-hidden h-[180px] flex items-center w-full">
                    <div className="absolute right-0 top-0 w-40 h-40 opacity-20">
                      <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill="#FFFFFF"
                          d="M45.3,-59.1C58.9,-51.1,70.2,-37.3,76.4,-21.1C82.6,-4.9,83.7,13.7,77.2,29.7C70.7,45.7,56.5,59.1,40.3,67.7C24.1,76.3,5.9,80,-11.4,77.2C-28.7,74.4,-45.1,65.1,-57.4,51.5C-69.8,37.9,-78.1,19.9,-79.1,1.1C-80.1,-17.8,-73.9,-35.6,-61.8,-47.8C-49.7,-60,-24.9,-66.5,-3.2,-62.8C18.5,-59.1,31.7,-67.1,45.3,-59.1Z"
                          transform="translate(100 100)"
                        />
                      </svg>
                    </div>
                    <div className="relative z-10">
                      <h2 className="text-2xl font-bold mb-2">คอร์สแนะนำสำหรับคุณ</h2>
                      <p className="mb-4 max-w-xl">เลือกจากคอร์สออนไลน์ด้านการแพทย์กว่า 50 คอร์ส พร้อมเนื้อหาใหม่ทุกเดือน</p>
                      <Link
                        href="/courses"
                        className="inline-block px-4 py-2 bg-white text-[#008268] rounded-md font-medium hover:bg-gray-50 transition-colors"
                      >
                        ดูคอร์สทั้งหมด
                      </Link>
                    </div>
                  </div>

                  {/* Your Courses */}
                  <div className="mb-8 w-full">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center gap-2">
                        <h2 className="text-xl font-bold text-gray-800">คอร์สของคุณ</h2>

                        {/* Filter dropdown */}
                        <div className="relative ml-2">
                          <button
                            onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                            className="flex items-center text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded hover:bg-gray-200"
                          >
                            <Filter size={14} className="mr-1" />
                            {currentFilter === "all"
                              ? "ทั้งหมด"
                              : currentFilter === "completed"
                                ? "เรียนจบแล้ว"
                                : "กำลังเรียน"}
                            <ChevronDown size={14} className="ml-1" />
                          </button>

                          {showFilterDropdown && (
                            <div className="absolute top-full left-0 mt-1 bg-white shadow-md rounded-md z-10 w-40 py-1">
                              <button
                                onClick={() => {
                                  setCurrentFilter("all")
                                  setShowFilterDropdown(false)
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-100"
                              >
                                {currentFilter === "all" ? (
                                  <CheckCircle size={14} className="mr-2 text-green-600" />
                                ) : (
                                  <Circle size={14} className="mr-2 text-gray-300" />
                                )}
                                ทั้งหมด
                              </button>
                              <button
                                onClick={() => {
                                  setCurrentFilter("completed")
                                  setShowFilterDropdown(false)
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-100"
                              >
                                {currentFilter === "completed" ? (
                                  <CheckCircle size={14} className="mr-2 text-green-600" />
                                ) : (
                                  <Circle size={14} className="mr-2 text-gray-300" />
                                )}
                                เรียนจบแล้ว
                              </button>
                              <button
                                onClick={() => {
                                  setCurrentFilter("in_progress")
                                  setShowFilterDropdown(false)
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-100"
                              >
                                {currentFilter === "in_progress" ? (
                                  <CheckCircle size={14} className="mr-2 text-green-600" />
                                ) : (
                                  <Circle size={14} className="mr-2 text-gray-300" />
                                )}
                                กำลังเรียน
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => scrollCourses("left")}
                          className="p-1 rounded-full bg-gray-100 hover:bg-gray-200"
                          aria-label="Scroll left"
                        >
                          <ChevronLeft size={20} />
                        </button>
                        <button
                          onClick={() => scrollCourses("right")}
                          className="p-1 rounded-full bg-gray-100 hover:bg-gray-200"
                          aria-label="Scroll right"
                        >
                          <ChevronRight size={20} />
                        </button>
                        <Link href="/profile/my-courses" className="text-sm text-[#008268] hover:underline">
                          ดูทั้งหมด
                        </Link>
                      </div>
                    </div>

                    {/* แสดงคอร์สที่ลงทะเบียนแล้ว */}
                    {filteredCourses.length > 0 ? (
                      <div
                        ref={coursesScrollRef}
                        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 w-full overflow-x-auto pb-4 hide-scrollbar"
                        style={{
                          scrollbarWidth: "none",
                          msOverflowStyle: "none",
                        }}
                      >
                        {filteredCourses.map((course) => (
                          <CourseCard
                            key={course.id}
                            id={course.id}
                            descriptrion={course.description}
                            name={course.name}
                            teacherName={course.teacher.name}
                            coverImage={course.coverImage || "/placeholder.svg"}
                            progress={course.progress || 0}
                            completedLessons={course.completedLessons || 0}
                            totalLessons={course.totalLessons || course.lesson.length}
                            duration={course.time}
                            status={course.LearningStatus as "completed" | "in_progress" | "not_started"}
                            level={course.level}
                            certify={course.certify}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="bg-white rounded-xl p-6 text-center w-full">
                        <p className="text-gray-600 mb-4">
                          {currentFilter === "all"
                            ? "คุณยังไม่มีคอร์สที่กำลังเรียนหรือเรียนจบแล้ว"
                            : currentFilter === "completed"
                              ? "คุณยังไม่มีคอร์สที่เรียนจบ"
                              : "คุณยังไม่มีคอร์สที่กำลังเรียน"}
                        </p>
                        <Link
                          href="/courses"
                          className="inline-block px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                        >
                          ดูคอร์สเรียนทั้งหมด
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Custom CSS for hiding scrollbar */}
        <style jsx global>{`
          .hide-scrollbar::-webkit-scrollbar {
            display: none;
          }
          .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          /* iPad Pro และอุปกรณ์ขนาดกลาง */
          @media (min-width: 768px) and (max-width: 1024px) {
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
            
            /* เพิ่ม padding ให้กับ content เพื่อให้มีพื้นที่มากขึ้น */
            .p-6 {
              padding: 1.25rem;
            }
            
            /* ปรับขนาด gap ระหว่างการ์ด */
            .gap-4 {
              gap: 1rem;
            }
          }
          
          /* สำหรับ iPad Pro แนวนอน */
          @media (min-width: 1024px) and (max-width: 1366px) {
            .grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr));
            }
          }
        `}</style>
      </div>
    </>
  )
}
