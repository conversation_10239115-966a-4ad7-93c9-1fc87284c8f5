import AdminLayout from "@/components/admin/layout"
import ExamEditor from "@/components/admin/exam-editor"

export default function EditExamPage({ params }: { params: { id: string } }) {
  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">แก้ไขข้อสอบท้ายบท</h1>
        <p className="text-gray-600">แก้ไขข้อมูลและคำถามของข้อสอบท้ายบท</p>
      </div>

      <ExamEditor examId={params.id} />
    </AdminLayout>
  )
}
