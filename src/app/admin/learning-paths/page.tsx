"use client"

import AdminLayout from "@/components/admin/layout"
import LearningPathsTable from "@/components/admin/learning-paths-table"
import { learningPathsData } from "@/data/learningPaths"

export default function LearningPathsPage() {
    return (
        <AdminLayout>
            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-2xl font-bold text-gray-800">จัดการเส้นทางการเรียนรู้</h1>
                    <p className="text-gray-600 mt-1">จัดการและสร้างเส้นทางการเรียนรู้สำหรับผู้เรียน</p>
                </div>

                {/* Learning Paths Table */}
                <div className="rounded-lg border mt-3 bg-white p-4 shadow-sm">
                    <LearningPathsTable learningPaths={learningPathsData} />
                </div>
            </div>
        </AdminLayout>
    )
}
