import AdminLayout from "@/components/admin/layout"
import LearningPathEditor from "@/components/admin/learning-paths-editor"
import { learningPathsData } from "@/data/learningPaths"
import { notFound } from "next/navigation"

interface EditLearningPathPageProps {
  params: {
    id: string
  }
}

export default function EditLearningPathPage({ params }: EditLearningPathPageProps) {
  const learningPath = learningPathsData.find((path) => path.id === params.id)

  if (!learningPath) {
    notFound()
  }

  return (
    <AdminLayout>
      <LearningPathEditor initialData={learningPath} mode="edit" />
    </AdminLayout>
  )
}
