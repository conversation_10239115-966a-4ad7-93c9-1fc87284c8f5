"use client"

import { use } from "react"
import AssignEditor from "@/components/admin/assign-path-editor"
import AdminLayout from "@/components/admin/layout"
import { getMedicalUsersData } from "@/data/allUsers"
import { getAssignedPathsByUserId } from "@/data/assignedPaths"


interface EditAssignPageProps {
  params: Promise<{ id: string }>
}

export default function EditAssignPage({ params }: EditAssignPageProps) {
  const { id: studentId } = use(params)

  // หาข้อมูลนักเรียน
  const student = getMedicalUsersData.find((user) => user.id === studentId)

  // หาเส้นทางที่มอบหมายแล้ว
  const assignedPaths = getAssignedPathsByUserId(studentId)

  if (!student) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">ไม่พบข้อมูลนักเรียน</h1>
          <p className="text-gray-600">ไม่สามารถหาข้อมูลนักเรียนที่ต้องการแก้ไขได้</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">แก้ไขเส้นทางการเรียนรู้</h1>
            <p className="text-gray-600 mt-1">
              แก้ไขการมอบหมายเส้นทางการเรียนรู้สำหรับ {student.firstname} {student.lastname}
            </p>
          </div>
        </div>

        <AssignEditor
          mode="edit"
          preSelectedStudentId={studentId}
          initialAssignedPaths={assignedPaths.map((a) => a.pathId)}
        />
      </div>
    </AdminLayout>
  )
}
