"use client"

import AdminLayout from "@/components/admin/layout"
import { AssignStudentsTable } from "@/components/admin/assign-paths-table"

export default function AssignPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">กำหนดเส้นทางการเรียนรู้</h1>
          <p className="text-muted-foreground">จัดการการมอบหมายเส้นทางการเรียนรู้ให้กับนักเรียนแต่ละคน</p>
        </div>
        <div className="rounded-lg border mt-3 bg-white p-4 shadow-sm">
          <AssignStudentsTable />
        </div>
      </div>
    </AdminLayout>
  )
}
