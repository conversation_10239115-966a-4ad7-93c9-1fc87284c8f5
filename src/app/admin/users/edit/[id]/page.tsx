import UserEditor from "@/components/admin/user-editor"
import AdminLayout from "@/components/admin/layout"

export default function EditUserPage({ params }: { params: { id: string } }) {
  return (
    <AdminLayout>
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">แก้ไขข้อมูลผู้ใช้</h1>
          <p className="text-gray-600">แก้ไขข้อมูลและสิทธิ์การใช้งานของผู้ใช้</p>
        </div>
        <div className="rounded-lg">
          <UserEditor userId={params.id} />
        </div>
      </div>
    </AdminLayout>
  )
}

