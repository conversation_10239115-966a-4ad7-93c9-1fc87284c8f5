"use client"

import AdminLayout from "@/components/admin/layout"
import { <PERSON>, BookOpen, FileText, TrendingUp, TrendingDown, <PERSON>, Clock, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChevronDown } from "lucide-react"
import ActiveUsersChart from "@/components/admin/charts/ActiveUsersChart"
import StudentProgressChart from "@/components/admin/charts/StudentProgressChart"
import GrowthMetrics from "@/components/admin/charts/GrowthMetrics"
import CertificationChart from "@/components/admin/charts/CertificationChart"
import DropoffFunnel<PERSON>hart from "@/components/admin/charts/DropoffFunnelChart"
import CourseProgressChart from "@/components/admin/charts/CourseProgressChart"

export default function AdminDashboardPage() {
  const stats = [
    {
      title: "ผู้ใช้ทั้งหมด",
      value: "1,245",
      change: "+12.5%",
      trend: "up",
      icon: <Users size={24} className="text-blue-500" />
    },
    {
      title: "คอร์สทั้งหมด",
      value: "24",
      change: "+8.3%",
      trend: "up",
      icon: <BookOpen size={24} className="text-green-500" />
    },
    {
      title: "เส้นทางการเรียนรู้",
      value: "12",
      change: "+16.7%",
      trend: "up",
      icon: <FileText size={24} className="text-purple-500" />
    },
    {
      title: "ใบรับรองที่ออก",
      value: "892",
      change: "+22.1%",
      trend: "up",
      icon: <Award size={24} className="text-yellow-500" />
    },
    {
      title: "เวลาเรียนเฉลี่ย",
      value: "45 นาที",
      change: "-5.2%",
      trend: "down",
      icon: <Clock size={24} className="text-indigo-500" />
    },
    {
      title: "ผู้ใช้ที่ใช้งาน",
      value: "156",
      change: "+18.9%",
      trend: "up",
      icon: <UserCheck size={24} className="text-emerald-500" />
    },
  ]

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">แดชบอร์ด</h1>
        <p className="text-gray-600">ยินดีต้อนรับสู่ระบบจัดการ E-MED LEARNING</p>
      </div>

      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                <h3 className="text-3xl font-bold text-gray-800 mt-1">{stat.value}</h3>
                <div className="flex items-center mt-2">
                  {stat.trend === "up" ? (
                    <TrendingUp size={16} className="text-green-500 mr-1" />
                  ) : (
                    <TrendingDown size={16} className="text-red-500 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">จากเดือนที่แล้ว</span>
                </div>
              </div>
              <div className="p-3 bg-gray-50 rounded-full">{stat.icon}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Active Users & Engagement Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-4">ผู้ใช้ที่ใช้งาน</h2>
          <ActiveUsersChart />
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-4">อัตราการเติบโต</h2>
          <GrowthMetrics />
        </div>
      </div>

      {/* Student Progress Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-4">การเดินทางของนักเรียน </h2>
          <DropoffFunnelChart />
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-4">ความคืบหน้าของคอร์ส</h2>
          <CourseProgressChart />
        </div>
      </div>

      {/* Course Selection Bar */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-800">เลือกคอร์สเรียนเพื่อดูรายละเอียด</h2>
        </div>
        <div className="relative">
          <select className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer">
            <option value="">เลือกคอร์สเรียน...</option>
            <option value="pathology-basics">Pathology Basics - พื้นฐานพยาธิวิทยา</option>
            <option value="clinical-skills">Clinical Skills - ทักษะคลินิก</option>
            <option value="pneumonia-treatment">Pneumonia Treatment - การรักษาปอดบวม</option>
            <option value="cardiac-care">Cardiac Care - การดูแลหัวใจ</option>
            <option value="emergency-medicine">Emergency Medicine - เวชศาสตร์ฉุกเฉิน</option>
            <option value="pediatric-care">Pediatric Care - การดูแลเด็ก</option>
            <option value="surgical-techniques">Surgical Techniques - เทคนิคการผ่าตัด</option>
            <option value="diagnostic-imaging">Diagnostic Imaging - การถ่ายภาพวินิจฉัย</option>
            <option value="pharmacology">Pharmacology - เภสัชวิทยา</option>
            <option value="anatomy-physiology">Anatomy & Physiology - กายวิภาคและสรีรวิทยา</option>
            <option value="medical-ethics">Medical Ethics - จริยธรรมทางการแพทย์</option>
            <option value="infection-control">Infection Control - การควบคุมการติดเชื้อ</option>
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={20} />
        </div>
        <p className="text-sm text-gray-500 mt-2">เลือกคอร์สเรียนเพื่อดูสถิติและความคืบหน้าของนักเรียนในคอร์สนั้น ๆ</p>
      </div>

      {/* Certification Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-4">ความคืบหน้าของนักเรียน</h2>
          <StudentProgressChart />
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-4">การออกใบรับรอง</h2>
          <CertificationChart />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h2 className="text-lg font-medium text-gray-800 mb-4">กิจกรรมล่าสุด</h2>
        <div className="space-y-4">
          {[
            { action: "ผู้ใช้ใหม่ได้ลงทะเบียนเข้าสู่ระบบ", time: "2 ชั่วโมงที่แล้ว", icon: Users },
            { action: "นักเรียนได้รับใบรับรอง Pathology", time: "3 ชั่วโมงที่แล้ว", icon: Award },
            { action: "คอร์สใหม่ 'Clinical Skills' ถูกเพิ่ม", time: "5 ชั่วโมงที่แล้ว", icon: BookOpen },
            { action: "นักเรียน 15 คนเสร็จสิ้นคอร์ส Pneumonia Treatment", time: "1 วันที่แล้ว", icon: FileText },
          ].map((activity, index) => {
            const IconComponent = activity.icon
            return (
              <div key={index} className="flex items-start pb-4 border-b border-gray-100 last:border-0 last:pb-0">
                <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                  <IconComponent size={20} className="text-gray-500" />
                </div>
                <div>
                  <p className="text-sm font-medium">{activity.action}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </AdminLayout>
  )
}
