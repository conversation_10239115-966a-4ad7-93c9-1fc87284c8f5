"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Navbar from "@/components/headers"
import axios from "axios"
import { jwtDecode } from "jwt-decode"

export default function LoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  })

  const [errors, setErrors] = useState({
    username: "",
    password: "",
  })

  const [loginError, setLoginError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoginError("")
    const newErrors: any = {}
    if (!formData.username) newErrors.username = "กรุณากรอกชื่อผู้ใช้"
    if (!formData.password) newErrors.password = "กรุณากรอกรหัสผ่าน"
    setErrors(newErrors)
    if (Object.keys(newErrors).length === 0) {
      try {
        const response = await axios.post("http://localhost:8000/auth/login", {
          username: formData.username,
          password: formData.password,
        })
        // The API returns { token: 'bearer <token>' }
        const bearerToken = response.data.token
        const token = bearerToken.replace(/^bearer /i, "")
        localStorage.setItem("access_token", token)
        const decoded: any = jwtDecode(token)
        // Save user info if needed
        localStorage.setItem("userRole", decoded.role)
        localStorage.setItem("userName", decoded.name)
        if (decoded.role === "admin") {
          router.push("/admin")
        } else {
          router.push("/profile/dashboard")
        }
      } catch (error: any) {
        setLoginError(
          error.response?.data?.error || "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง"
        )
      }
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  return (
    <>
      {/* Background Image - SypheatBuilding */}
      <div className="min-h-screen relative">
        <div className="absolute inset-0 z-0">
          <Image
            src="/e-med/img/SynphaetBuilding.jpg"
            alt="Sypheat Building Background"
            fill
            className="object-cover "
            priority
          />
          {/* Dark overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20 " />
        </div>

        {/* Content */}
        <div className="relative z-10 min-h-screen flex justify-center items-center">
          {/* Main Content */}
          <div className="w-full max-w-4xl">
            <div className=" backdrop-blur-[2px] border border-white lg:w-[100vh] lg:h-[60vh] ipad-pro:w-[70vh]  ipad-pro:h-[45vh] rounded-2xl overflow-hidden shadow-2xl flex flex-col md:flex-row">
              {/* Left Side - Background Image */}
              <div className="relative hidden md:block w-1/2">
                <div className="absolute inset-0  z-10" />

                <div className="absolute top-16 mx-28 -left-16 text- p-4 z-20">
                  <h1 className="bg-gradient-to-r  from-[#2e8574] to-[#293D97] leading-snug text-5xl font-extrabold bg-clip-text text-transparent">
                    E-MED LEARNING
                  </h1>
                </div>
              </div>

              {/* Right Side - Login Form */}
              <div className="w-full md:w-1/2 p-8 flex flex-col h-full  bg-white border-l border-white/20"> <h2 className="text-3xl font-bold text-center mt-10 mb-5 text-black">เข้าสู่ระบบ</h2>

                {loginError && (
                  <div className="bg-red-500/20 backdrop-blur-sm border border-red-300/50 text-red-700 px-4 py-3 rounded-md mb-4">
                    {loginError}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6 flex flex-col h-full">
                  <div className="flex-grow space-y-6">
                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                        ชื่อผู้ใช้
                      </label>
                      <input
                        id="username"
                        name="username"
                        type="text"
                        required
                        value={formData.username}
                        onChange={handleChange}
                        className="w-full px-3 py-2 bg-white/30 backdrop-blur-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1] text-gray-800 placeholder-gray-600"
                      />
                      {errors.username && <p className="text-red-600 text-xs mt-1">{errors.username}</p>}
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        รหัสผ่าน
                      </label>
                      <input
                        id="password"
                        name="password"
                        type="password"
                        required
                        value={formData.password}
                        onChange={handleChange}
                        className="w-full px-3 py-2 bg-white/30 backdrop-blur-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1] text-gray-800 placeholder-gray-600"
                      />
                      {errors.password && <p className="text-red-600 text-xs mt-1">{errors.password}</p>}
                    </div>

                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <input
                          id="rememberMe"
                          name="rememberMe"
                          type="checkbox"
                          checked={formData.rememberMe}
                          onChange={handleChange}
                          className="h-4 w-4 text-[#008268] focus:ring-[#2FBCC1] border-gray-300 rounded bg-white/30"
                        />
                        <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
                          จดจำฉัน
                        </label>
                      </div>
                      <div className="text-sm">
                        <Link href="/forgot-password" className="font-medium text-[#008268] hover:text-[#6aafa1]">
                          ลืมรหัสผ่าน
                        </Link>
                      </div>
                    </div>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-[#008268] hover:bg-[#6aafa1]/90 text-white font-bold text-lg py-2 px-4 rounded-md lg:mt-auto shadow-lg"
                  >
                    เข้าสู่ระบบ
                  </button>
                </form>

                <div className="text-center mt-4">
                  <p className="text-sm text-gray-600">
                    ยังไม่เป็นสมาชิก?{" "}
                    <Link href="/register" className="font-medium text-[#008268] hover:text-[#6aafa1]">
                      สมัครสมาชิก
                    </Link>
                  </p>
                </div>

                {/* ข้อมูลสำหรับการทดสอบ */}
                <div className="mt-4 pt-4 border-t border-white/30">
                  <p className="text-xs text-gray-600 text-center">ข้อมูลสำหรับการทดสอบ:</p>
                  <div className="grid grid-cols-2 gap-2 mt-2 text-xs text-gray-600">
                    <div className="bg-white/20 backdrop-blur-sm rounded p-2">
                      <p className="font-medium">นักศึกษา:</p>
                      <p><EMAIL></p>
                      <p>student123</p>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded p-2">
                      <p className="font-medium">อาจารย์:</p>
                      <p><EMAIL></p>
                      <p>password123</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
