"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { useRouter, useSearchParams } from "next/navigation"

export default function VerifyOTPPage() {
  const searchParams = useSearchParams()
  const email = searchParams.get("email") || ""
  const maskedEmail = email ? email.replace(/(.{2})(.*)(@.*)/, "$1*****$3") : ""

  const [otp, setOtp] = useState(["", "", "", "", "", ""])
  const [error, setError] = useState("")
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])
  const router = useRouter()

  // Pre-fill first two digits for demo purposes
  useEffect(() => {
    setOtp(["6", "1", "", "", "", ""])
  }, [])

  const handleChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d+$/.test(value)) return

    const newOtp = [...otp]
    newOtp[index] = value.slice(0, 1) // Only take the first character

    setOtp(newOtp)

    // Move to next input if current input is filled
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Check if OTP is complete
    if (otp.some((digit) => !digit)) {
      setError("กรุณากรอกรหัส OTP ให้ครบ")
      return
    }

    // Here you would typically validate the OTP with your backend
    console.log("Verifying OTP:", otp.join(""))

    // For demo purposes, we'll just navigate to the reset password page
    router.push(`/reset-password?email=${encodeURIComponent(email)}`)
  }

  const handleResendOTP = () => {
    console.log("Resending OTP to:", email)
    // Implement your resend OTP logic here
  }

  return (
    <>
      {/* Background Image - SypheatBuilding */}
      <div className="min-h-screen relative">
        <div className="absolute inset-0 z-0">
          <Image
            src="/e-med/img/SynphaetBuilding.jpg"
            alt="Sypheat Building Background"
            fill
            className="object-cover"
            priority
          />
          {/* Dark overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20" />
        </div>

        {/* Content */}
        <div className="relative z-10 min-h-screen flex justify-center items-center">
          {/* Main Content */}
          <div className="w-full max-w-4xl">
            <div className="backdrop-blur-[2px] border border-white rounded-2xl lg:w-[100vh] md:h-[40vh] ipad-pro:w-[60vh] ipad-pro:h-[40vh] lg:h-[60vh] overflow-hidden shadow-2xl flex flex-col md:flex-row">
              {/* Left Side - Background Image */}
              <div className="relative hidden md:block w-1/2">
                <div className="absolute inset-0 backdrop-blur z-10" />
                <div className="absolute top-16 mx-28 -left-16 text- p-4 z-20">
                  <h1 className="bg-gradient-to-r from-[#2e8574] to-[#293D97] leading-snug text-5xl font-extrabold bg-clip-text text-transparent">
                    E-MED LEARNING
                  </h1>
                </div>
              </div>

              {/* Right Side - OTP Verification Form */}
              <div className="w-full md:w-1/2 p-8 flex flex-col justify-center  bg-white border-l border-white/20">
                <h2 className="text-3xl font-bold text-center mt-10 mb-5 text-gray-800">กรอกรหัสยืนยัน</h2>

                <p className="text-center text-sm text-gray-700 mb-6">
                  กรอกรหัส OTP ที่ส่งไปยังอีเมล {maskedEmail}
                  <br />
                  ในการตั้งค่ารหัสผ่านใหม่เพื่อความปลอดภัยของบัญชี
                </p>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="flex justify-center space-x-2">
                    {otp.map((digit, index) => (
                      <input
                        key={index}
                        ref={(el) => {
                          inputRefs.current[index] = el
                        }}
                        type="text"
                        value={digit}
                        onChange={(e) => handleChange(index, e.target.value)}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                        className="w-14 h-16 text-center text-2xl bg-gray-100 backdrop-blur-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3B4A8F] text-gray-800 placeholder-gray-600"
                        maxLength={1}
                      />
                    ))}
                  </div>

                  {error && <p className="text-red-600 text-xs text-center mt-1">{error}</p>}

                  <button
                    type="submit"
                    className="w-full bg-[#3B4A8F] hover:bg-[#3B4A8F]/90 text-white font-bold text-lg py-2 px-4 rounded-md shadow-lg"
                  >
                    ยืนยันรหัสผ่าน
                  </button>
                </form>

                <div className="text-center mt-4">
                  <p className="text-sm text-gray-700">
                    ยังไม่ได้รับรหัส?{" "}
                    <button onClick={handleResendOTP} className="font-medium text-[#3B4A8F] hover:text-[#3B4A8F]/80">
                      ส่งรหัสผ่านอีกครั้ง
                    </button>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
