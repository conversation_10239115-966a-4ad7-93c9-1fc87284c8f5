// ปรับปรุงโครงสร้างข้อมูลให้สอดคล้องกับฟอร์มสร้างควิซ
// และเพิ่มฟังก์ชันสำหรับดึงข้อมูลแบบไม่ตายตัว

export interface Choice {
  id: string
  content: string
  isCorrect: boolean
  imageUrl?: string
  type?: string
}

export interface Question {
  id: string
  title: string
  content?: string
  imageUrl?: string
  videoTimestamp?: number
  choices: Choice[]
  type?: string
}

export interface QuizData {
  id: string
  name: string
  description?: string
  questions: Question[]
  passingScore: number
  timeLimit?: number
  course?: string
  courseId?: string
  lessonId?: string
  contentId?: string
  status?: string
  questionCount?: number
}

// ปรับโครงสร้างข้อมูล QuizData โดยกำหนดค่า passingScore เป็น 100 เสมอ

// แก้ไขส่วนข้อมูลควิซตัวอย่าง
const quizDataStore: Record<string, Record<string, Record<string, QuizData>>> = {
  // courseId -> lessonId -> contentId -> QuizData
  WnH5F0qZF938: {
    "627BieXQX84E": {
      iL79E9pow1kh: {
        id: "quiz-1",
        name: "ควิซโรคปอดอักเสบ",
        description: "ควิซเพื่อทดสอบความเข้าใจเกี่ยวกับโรคปอดอักเสบ",
        course: "การปฐมพยาบาลเบื้องต้น",
        courseId: "WnH5F0qZF938",
        lessonId: "627BieXQX84E",
        contentId: "iL79E9pow1kh",
        status: "published",
        questions: [
          {
            id: "q1",
            title: "สาเหตุหลักของโรคปอดอักเสบคือข้อใด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 10,
            choices: [
              {
                id: "c1",
                content: "การติดเชื้อไวรัสเท่านั้น",
                isCorrect: false,
              },
              {
                id: "c2",
                content: "การติดเชื้อแบคทีเรีย เชื้อไวรัส หรือเชื้อรา",
                isCorrect: true,
              },
              {
                id: "c3",
                content: "มลภาวะทางอากาศเท่านั้น",
                isCorrect: false,
              },
              {
                id: "c4",
                content: "การแพ้อากาศ",
                isCorrect: false,
              },
            ],
          },
          {
            id: "q2",
            title: "อาการที่พบบ่อยในผู้ป่วยโรคปอดอักเสบคือข้อใด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 20,
            choices: [
              {
                id: "c1",
                content: "ไข้สูง ไอมีเสมหะ หายใจลำบาก",
                isCorrect: true,
              },
              {
                id: "c2",
                content: "ท้องเสีย อาเจียน ปวดท้อง",
                isCorrect: false,
              },
              {
                id: "c3",
                content: "ผื่นคัน ปวดข้อ ปวดกล้ามเนื้อ",
                isCorrect: false,
              },
              {
                id: "c4",
                content: "ปวดศีรษะ มึนงง ชาตามแขนขา",
                isCorrect: false,
              },
            ],
          },
        ],
        passingScore: 100,
        timeLimit: 300,
      },
    },
    yjLvq2n9296q: {
      "8l25jy3S82Xz": {
        id: "quiz-vaccine",
        name: "ควิซการป้องกันโรคปอดอักเสบด้วยวัคซีน",
        description: "ควิซเพื่อทดสอบความเข้าใจเกี่ยวกับการป้องกันโรคปอดอักเสบด้วยวัคซีน PCV และ PPSV",
        course: "การป้องกันโรคติดเชื้อ",
        courseId: "WnH5F0qZF938",
        lessonId: "yjLvq2n9296q",
        contentId: "8l25jy3S82Xz",
        status: "published",
        questions: [
          {
            id: "v1",
            title: "วัคซีน PCV (Pneumococcal Conjugate Vaccine) เหมาะสำหรับกลุ่มใด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 10,
            choices: [
              {
                id: "vc1",
                content: "เด็กอายุต่ำกว่า 2 ปี",
                isCorrect: true,
              },
              {
                id: "vc2",
                content: "ผู้สูงอายุเท่านั้น",
                isCorrect: false,
              },
              {
                id: "vc3",
                content: "ผู้ที่มีอายุมากกว่า 65 ปีเท่านั้น",
                isCorrect: false,
              },
              {
                id: "vc4",
                content: "ผู้ที่เคยเป็นโรคปอดอักเสบมาแล้วเท่านั้น",
                isCorrect: false,
              },
            ],
          },
          {
            id: "v2",
            title: "วัคซีน PPSV (Pneumococcal Polysaccharide Vaccine) แนะนำให้ฉีดในกลุ่มใด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 70,
            choices: [
              {
                id: "vc1",
                content: "เด็กแรกเกิด",
                isCorrect: false,
              },
              {
                id: "vc2",
                content: "ผู้ใหญ่อายุ 19-64 ปีที่มีโรคประจำตัว",
                isCorrect: true,
              },
              {
                id: "vc3",
                content: "เด็กอายุต่ำกว่า 2 ปีเท่านั้น",
                isCorrect: false,
              },
              {
                id: "vc4",
                content: "ผู้ที่แพ้ยาปฏิชีวนะ",
                isCorrect: false,
              },
            ],
          },
          {
            id: "v3",
            title: "วัคซีน PCV มีกี่ชนิด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 110,
            choices: [
              {
                id: "vc1",
                content: "1 ชนิด",
                isCorrect: false,
              },
              {
                id: "vc2",
                content: "2 ชนิด",
                isCorrect: false,
              },
              {
                id: "vc3",
                content: "3 ชนิด (PCV7, PCV10, PCV13)",
                isCorrect: true,
              },
              {
                id: "vc4",
                content: "4 ชนิด",
                isCorrect: false,
              },
            ],
          },
          {
            id: "v4",
            title: "ผลข้างเคียงที่พบบ่อยหลังฉีดวัคซีนป้องกันโรคปอดอักเสบคือข้อใด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 150,
            choices: [
              {
                id: "vc1",
                content: "ไข้สูง ชัก",
                isCorrect: false,
              },
              {
                id: "vc2",
                content: "ปวด บวม แดงบริเวณที่ฉีด และไข้ต่ำ",
                isCorrect: true,
              },
              {
                id: "vc3",
                content: "ผื่นลมพิษทั่วตัว",
                isCorrect: false,
              },
              {
                id: "vc4",
                content: "อาการแพ้รุนแรง (Anaphylaxis)",
                isCorrect: false,
              },
            ],
          },
          {
            id: "v5",
            title: "ข้อใดเป็นข้อห้ามในการฉีดวัคซีนป้องกันโรคปอดอักเสบ?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 190,
            choices: [
              {
                id: "vc1",
                content: "มีประวัติแพ้ส่วนประกอบของวัคซีนอย่างรุนแรง",
                isCorrect: true,
              },
              {
                id: "vc2",
                content: "มีไข้ต่ำๆ",
                isCorrect: false,
              },
              {
                id: "vc3",
                content: "เคยเป็นโรคปอดอักเสบมาก่อน",
                isCorrect: false,
              },
              {
                id: "vc4",
                content: "อายุมากกว่า 65 ปี",
                isCorrect: false,
              },
            ],
          },
        ],
        passingScore: 100,
        timeLimit: 420,
      },
    },
  },
  "QlH3I647MCuN": {
    "914VEz100Wjs": {
      "EP9sCqtUAe85": {
        id: "quiz-2",
        name: "ควิซโรคซึมเศร้า",
        description: "ควิซเพื่อทดสอบความเข้าใจเกี่ยวกับโรคซึมเศร้า",
        course: "สุขภาพจิต",
        courseId: "QlH3I647MCuN",
        lessonId: "914VEz100Wjs",
        contentId: "EP9sCqtUAe85",
        status: "published",
        questions: [
          {
            id: "d1",
            title: "อาการสำคัญของโรคซึมเศร้าคือข้อใด?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 10,
            choices: [
              {
                id: "dc1",
                content: "อารมณ์เศร้า เบื่อหน่าย สูญเสียความสนใจหรือความเพลิดเพลินในกิจกรรมต่างๆ",
                isCorrect: true,
              },
              {
                id: "dc2",
                content: "หวาดระแวง หูแว่ว ประสาทหลอน",
                isCorrect: false,
              },
              {
                id: "dc3",
                content: "พูดเร็ว คิดเร็ว ใช้เงินฟุ่มเฟือย",
                isCorrect: false,
              },
              {
                id: "dc4",
                content: "กลัวสถานที่แคบ กลัวที่สูง กลัวแมลง",
                isCorrect: false,
              },
            ],
          },
          {
            id: "d2",
            title: "ปัจจัยใดที่เพิ่มความเสี่ยงต่อการเกิดโรคซึมเศร้า?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 20,
            choices: [
              {
                id: "dc1",
                content: "การออกกำลังกายสม่ำเสมอ",
                isCorrect: false,
              },
              {
                id: "dc2",
                content: "การมีเครือข่ายสังคมที่เข้มแข็ง",
                isCorrect: false,
              },
              {
                id: "dc3",
                content: "ประวัติครอบครัวเป็นโรคซึมเศร้า ความเครียดสะสม และการสูญเสียคนใกล้ชิด",
                isCorrect: true,
              },
              {
                id: "dc4",
                content: "การนอนหลับเพียงพอ",
                isCorrect: false,
              },
            ],
          },
        ],
        passingScore: 100, // กำหนดเป็น 100% เสมอ
        timeLimit: 180,
      },
    },
    c4mT32O7RboV: {
      "2xmX4V5OKG3l": {
        id: "quiz-depression-2",
        name: "ควิซภาวะซึมเศร้าและโรคทางใจ",
        description: "ควิซเพื่อทดสอบความเข้าใจเกี่ยวกับภาวะซึมเศร้า โรคทางใจ และความผิดปกติทางอารมณ์",
        course: "สุขภาพจิต",
        courseId: "QlH3I647MCuN",
        lessonId: "c4mT32O7RboV",
        contentId: "2xmX4V5OKG3l",
        status: "published",
        questions: [
          {
            id: "md1",
            title: "ข้อใดเป็นความแตกต่างระหว่างภาวะเศร้าปกติกับโรคซึมเศร้า?",
            content: "เลือกข้อที่ถูกต้องที่สุด",
            videoTimestamp: 10,
            choices: [
              {
                id: "mdc1",
                content:
                  "ภาวะเศร้าปกติเกิดจากสถานการณ์และหายไปเมื่อสถานการณ์คลี่คลาย ส่วนโรคซึมเศร้าเป็นต่อเนื่องนานกว่า 2 สัปดาห์และส่งผลกระทบต่อการดำเนินชีวิต",
                isCorrect: true,
              },
              {
                id: "mdc2",
                content: "ภาวะเศร้าปกติและโรคซึมเศร้าไม่มีความแตกต่างกัน",
                isCorrect: false,
              },
              {
                id: "mdc3",
                content: "ภาวะเศร้าปกติต้องรักษาด้วยยา ส่วนโรคซึมเศร้าไม่จำเป็นต้องรักษา",
                isCorrect: false,
              },
              {
                id: "mdc4",
                content: "ภาวะเศร้าปกติเกิดในผู้สูงอายุเท่านั้น ส่วนโรคซึมเศร้าเกิดในวัยรุ่นเท่านั้น",
                isCorrect: false,
              },
            ],
          },
        ],
        passingScore: 100,
        timeLimit: 360,
      }

    },
  },
}

// ฟังก์ชันสำหรับดึงข้อมูลควิซตาม courseId, lessonId และ contentId
export function getQuizData(courseId: string, lessonId: string, contentId: string): QuizData | null {
  try {
    return quizDataStore[courseId]?.[lessonId]?.[contentId] || null
  } catch (error) {
    console.error("Error getting quiz data:", error)
    return null
  }
}

// ฟังก์ชันสำหรับดึงข้อมูลควิซทั้งหมด (สำหรับตาราง)
export function getAllQuizzes(): QuizData[] {
  const allQuizzes: QuizData[] = []

  try {
    // วนลูปผ่านทุก courseId
    Object.keys(quizDataStore).forEach((courseId) => {
      // วนลูปผ่านทุก lessonId ในแต่ละ course
      Object.keys(quizDataStore[courseId]).forEach((lessonId) => {
        // วนลูปผ่านทุก contentId ในแต่ละ lesson
        Object.keys(quizDataStore[courseId][lessonId]).forEach((contentId) => {
          const quiz = quizDataStore[courseId][lessonId][contentId]

          // เพิ่ม questionCount ถ้ายังไม่มี
          if (!quiz.questionCount && quiz.questions) {
            quiz.questionCount = quiz.questions.length
          }

          allQuizzes.push(quiz)
        })
      })
    })

    return allQuizzes
  } catch (error) {
    console.error("Error getting all quizzes:", error)
    return []
  }
}

// ฟังก์ชันสำหรับดึงข้อมูลควิซตาม ID
export function getQuizById(quizId: string): QuizData | null {
  try {
    // วนลูปผ่านทุก courseId
    for (const courseId of Object.keys(quizDataStore)) {
      // วนลูปผ่านทุก lessonId ในแต่ละ course
      for (const lessonId of Object.keys(quizDataStore[courseId])) {
        // วนลูปผ่านทุก contentId ในแต่ละ lesson
        for (const contentId of Object.keys(quizDataStore[courseId][lessonId])) {
          const quiz = quizDataStore[courseId][lessonId][contentId]
          if (quiz.id === quizId) {
            return quiz
          }
        }
      }
    }

    return null
  } catch (error) {
    console.error("Error getting quiz by ID:", error)
    return null
  }
}

// แก้ไขฟังก์ชัน saveQuiz ให้กำหนดค่า passingScore เป็น 100 เสมอ
export function saveQuiz(quiz: QuizData): boolean {
  try {
    if (!quiz.courseId || !quiz.lessonId || !quiz.contentId) {
      console.error("Missing required fields: courseId, lessonId, or contentId")
      return false
    }

    // กำหนดค่า passingScore เป็น 100 เสมอ
    quiz.passingScore = 100

    // ตรวจสอบว่ามี courseId ในข้อมูลหรือไม่
    if (!quizDataStore[quiz.courseId]) {
      quizDataStore[quiz.courseId] = {}
    }

    // ตรวจสอบว่ามี lessonId ในข้อมูลหรือไม่
    if (!quizDataStore[quiz.courseId][quiz.lessonId]) {
      quizDataStore[quiz.courseId][quiz.lessonId] = {}
    }

    // บันทึกข้อมูลควิซ
    quizDataStore[quiz.courseId][quiz.lessonId][quiz.contentId] = quiz

    return true
  } catch (error) {
    console.error("Error saving quiz:", error)
    return false
  }
}

// ฟังก์ชันสำหรับลบข้อมูลควิซ (จำลอง)
export function deleteQuiz(quizId: string): boolean {
  try {
    // วนลูปผ่านทุก courseId
    for (const courseId of Object.keys(quizDataStore)) {
      // วนลูปผ่านทุก lessonId ในแต่ละ course
      for (const lessonId of Object.keys(quizDataStore[courseId])) {
        // วนลูปผ่านทุก contentId ในแต่ละ lesson
        for (const contentId of Object.keys(quizDataStore[courseId][lessonId])) {
          const quiz = quizDataStore[courseId][lessonId][contentId]
          if (quiz.id === quizId) {
            // ลบข้อมูลควิซ
            delete quizDataStore[courseId][lessonId][contentId]
            return true
          }
        }
      }
    }

    return false
  } catch (error) {
    console.error("Error deleting quiz:", error)
    return false
  }
}
