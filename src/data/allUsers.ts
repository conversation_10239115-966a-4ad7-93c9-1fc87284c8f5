export interface MedicalUser {
    id: string
    firstname: string
    lastname: string
    email: string
    position: string
    lastLogin: Date
    status: "active" | "inactive"
    role: "student" | "lecturer" | "admin"
    password: string
    profileImage?: string
  }
  
  export const getMedicalUsersData: MedicalUser[] = [
    {
      id: "user-001",
      firstname: "สมชาย",
      lastname: "ใจดี",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านหัวใจ",
      lastLogin: new Date("2023-11-28T08:30:00"),
      status: "active",
      role: "admin",
      password: "password123",
    },
    {
      id: "user-002",
      firstname: "สมหญิง",
      lastname: "รักษาดี",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านอายุรกรรม",
      lastLogin: new Date("2023-11-27T14:45:00"),
      status: "active",
      role: "admin",
      password: "password123",
    },
    {
      id: "user-003",
      firstname: "วิชัย",
      lastname: "ฟื้นฟูดี",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านเวชศาสตร์ฟื้นฟู",
      lastLogin: new Date("2023-11-25T09:15:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-004",
      firstname: "นภา",
      lastname: "อาหารดี",
      email: "<EMAIL>",
      position: "นักโภชนาการ",
      lastLogin: new Date("2023-11-26T11:20:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-005",
      firstname: "ปฐม",
      lastname: "ช่วยเหลือ",
      email: "<EMAIL>",
      position: "แพทย์ฉุกเฉิน",
      lastLogin: new Date("2023-11-28T07:10:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-006",
      firstname: "เภสัช",
      lastname: "ยาดี",
      email: "<EMAIL>",
      position: "เภสัชกร",
      lastLogin: new Date("2023-11-27T16:30:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-007",
      firstname: "จิตรา",
      lastname: "ใจดี",
      email: "<EMAIL>",
      position: "จิตแพทย์",
      lastLogin: new Date("2023-11-24T13:45:00"),
      status: "inactive",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-008",
      firstname: "เด็กดี",
      lastname: "รักษาเด็ก",
      email: "<EMAIL>",
      position: "กุมารแพทย์",
      lastLogin: new Date("2023-11-23T10:20:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-009",
      firstname: "ผู้สูงวัย",
      lastname: "ใจดี",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านผู้สูงอายุ",
      lastLogin: new Date("2023-11-22T09:30:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-010",
      firstname: "รักษา",
      lastname: "ยาวนาน",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านโรคเรื้อรัง",
      lastLogin: new Date("2023-11-20T14:15:00"),
      status: "inactive",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-011",
      firstname: "ข้อดี",
      lastname: "เคลื่อนไหวคล่อง",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านออร์โธปิดิกส์",
      lastLogin: new Date("2023-11-18T11:40:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-012",
      firstname: "ภูมิคุ้ม",
      lastname: "แพ้น้อย",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านภูมิแพ้",
      lastLogin: new Date("2023-11-15T10:30:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-013",
      firstname: "ผิวสวย",
      lastname: "หนังใส",
      email: "<EMAIL>",
      position: "แพทย์ผิวหนัง",
      lastLogin: new Date("2023-11-10T13:20:00"),
      status: "inactive",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-014",
      firstname: "ท้องดี",
      lastname: "อาหารย่อย",
      email: "<EMAIL>",
      position: "แพทย์ระบบทางเดินอาหาร",
      lastLogin: new Date("2023-11-05T09:45:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-015",
      firstname: "ตับดี",
      lastname: "พักฟื้น",
      email: "<EMAIL>",
      position: "แพทย์ผู้เชี่ยวชาญด้านตับ",
      lastLogin: new Date("2023-11-01T15:30:00"),
      status: "active",
      role: "lecturer",
      password: "password123",
    },
    {
      id: "user-016",
      firstname: "จารุพัฒน์",
      lastname: "เกียรติ์ชัยสิริพร",
      email: "<EMAIL>",
      position: "ผู้ดูแลระบบ",
      lastLogin: new Date("2023-11-16T10:00:00"),
      status: "active",
      role: "admin",
      password: "admin123",
    },
    {
      id: "user-017",
      firstname: "จารุพัฒน์",
      lastname: "เกียรติ์ชัยสิริพร ณ อยุธยา",
      email: "<EMAIL>",
      position: "นักศึกษาแพทย์",
      lastLogin: new Date("2023-11-15T09:20:00"),
      status: "active",
      role: "student",
      password: "student123",
      profileImage: "",
    },
    {
      id: "user-018",
      firstname: "นักเรียน",
      lastname: "เรียนดี",
      email: "<EMAIL>",
      position: "นักศึกษาแพทย์",
      lastLogin: new Date("2023-11-14T14:30:00"),
      status: "active",
      role: "student",
      password: "student123",
    },
    {
      id: "user-019",
      firstname: "ศึกษา",
      lastname: "ค้นคว้า",
      email: "<EMAIL>",
      position: "นักศึกษาแพทย์",
      lastLogin: new Date("2023-11-13T11:15:00"),
      status: "active",
      role: "student",
      password: "student123",
    },
    {
      id: "user-020",
      firstname: "เรียนรู้",
      lastname: "ตั้งใจ",
      email: "<EMAIL>",
      position: "นักศึกษาแพทย์",
      lastLogin: new Date("2023-11-12T10:45:00"),
      status: "active",
      role: "student",
      password: "student123",
    },
    {
      id: "user-021",
      firstname: "ทดสอบ",
      lastname: "ตั้งใจ",
      email: "<EMAIL>",
      position: "นักศึกษาแพทย์",
      lastLogin: new Date("2023-11-12T10:45:00"),
      status: "active",
      role: "student",
      password: "student123",
    },
  ]
  