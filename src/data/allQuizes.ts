export const getMedicalQuizData = [
    {
      id: "quiz-001",
      name: "การวินิจฉัยโรคหัวใจเบื้องต้น",
      course: "การวินิจฉัยโรคเบื้องต้น",
      courseId: "1",
      lessonId: "627BieXQX84E", // เพิ่ม lessonId
      contentId: "content-1", // เพิ่ม contentId
      questionCount: 15,
      status: "published",
      createdBy: "ดร.สมชาย ใจดี",
      createdAt: "2023-10-15",
    },
    {
      id: "quiz-002",
      name: "การตรวจวัดความดันโลหิต",
      course: "การวินิจฉัยโรคเบื้องต้น",
      courseId: "1",
      lessonId: "627BieXQX84E", // เพิ่ม lessonId
      contentId: "content-2", // เพิ่ม contentId
      questionCount: 10,
      status: "published",
      createdBy: "ดร.สมหญิง รักษาดี",
      createdAt: "2023-10-18",
    },
    {
      id: "quiz-003",
      name: "การวินิจฉัยโรคเบาหวาน",
      course: "การวินิจฉัยโรคเบื้องต้น",
      courseId: "1",
      lessonId: "c4mT32O7RboV", // เพิ่ม lessonId
      contentId: "content-3", // เพิ่ม contentId
      questionCount: 12,
      status: "draft",
      createdBy: "ดร.สมชาย ใจดี",
      createdAt: "2023-10-20",
    },
    {
      id: "quiz-004",
      name: "การปฐมพยาบาลเบื้องต้น",
      course: "การปฐมพยาบาลเบื้องต้น",
      courseId: "10",
      lessonId: "lesson-3", // เพิ่ม lessonId
      contentId: "content-9", // เพิ่ม contentId
      questionCount: 20,
      status: "published",
      createdBy: "ดร.ปฐม ช่วยเหลือ",
      createdAt: "2023-10-22",
    },
    {
      id: "quiz-005",
      name: "การดูแลผู้ป่วยโรคหลอดเลือดสมอง",
      course: "การดูแลผู้ป่วยฉุกเฉิน",
      courseId: "3",
      lessonId: "lesson-3", // เพิ่ม lessonId
      contentId: "content-9", // เพิ่ม contentId
      questionCount: 18,
      status: "draft",
      createdBy: "ดร.วิชัย ฟื้นฟูดี",
      createdAt: "2023-10-25",
    },
    {
      id: "quiz-006",
      name: "การใช้ยาปฏิชีวนะอย่างเหมาะสม",
      course: "การใช้ยาอย่างปลอดภัย",
      courseId: "7",
      lessonId: "lesson-1", // เพิ่ม lessonId
      contentId: "content-5", // เพิ่ม contentId
      questionCount: 15,
      status: "published",
      createdBy: "ดร.เภสัช ยาดี",
      createdAt: "2023-10-28",
    },
    {
      id: "quiz-007",
      name: "การตรวจคัดกรองมะเร็ง",
      course: "การรักษาโรคทั่วไป",
      courseId: "2",
      lessonId: "lesson-1", // เพิ่ม lessonId
      contentId: "content-5", // เพิ่ม contentId
      questionCount: 12,
      status: "published",
      createdBy: "ดร.คัดกรอง เร็วดี",
      createdAt: "2023-11-01",
    },
    {
      id: "quiz-008",
      name: "การดูแลผู้ป่วยโรคไต",
      course: "การดูแลผู้ป่วยโรคเรื้อรัง",
      courseId: "11",
      lessonId: "lesson-2", // เพิ่ม lessonId
      contentId: "content-7", // เพิ่ม contentId
      questionCount: 14,
      status: "draft",
      createdBy: "ดร.ไตดี รักษาไว",
      createdAt: "2023-11-05",
    },
    {
      id: "quiz-009",
      name: "การดูแลผู้ป่วยโรคปอด",
      course: "การดูแลผู้ป่วยโรคเรื้อรัง",
      courseId: "11",
      lessonId: "lesson-2", // เพิ่ม lessonId
      contentId: "content-7", // เพิ่ม contentId
      questionCount: 16,
      status: "published",
      createdBy: "ดร.ปอดใส หายใจดี",
      createdAt: "2023-11-08",
    },
    {
      id: "quiz-010",
      name: "การดูแลผู้ป่วยโรคกระดูกพรุน",
      course: "การฟื้นฟูสมรรถภาพผู้ป่วย",
      courseId: "5",
      lessonId: "lesson-3", // เพิ่ม lessonId
      contentId: "content-9", // เพิ่ม contentId
      questionCount: 10,
      status: "disabled",
      createdBy: "ดร.กระดูก แข็งแรง",
      createdAt: "2023-11-10",
    },
    {
      id: "quiz-011",
      name: "การดูแลผู้ป่วยโรคข้อเสื่อม",
      course: "การฟื้นฟูสมรรถภาพผู้ป่วย",
      courseId: "5",
      lessonId: "lesson-4", // เพิ่ม lessonId
      contentId: "content-11", // เพิ่ม contentId
      questionCount: 12,
      status: "published",
      createdBy: "ดร.ข้อดี เคลื่อนไหวคล่อง",
      createdAt: "2023-11-12",
    },
    {
      id: "quiz-012",
      name: "การดูแลผู้ป่วยโรคภูมิแพ้",
      course: "การรักษาโรคทั่วไป",
      courseId: "2",
      lessonId: "lesson-4", // เพิ่ม lessonId
      contentId: "content-11", // เพิ่ม contentId
      questionCount: 15,
      status: "published",
      createdBy: "ดร.ภูมิคุ้ม แพ้น้อย",
      createdAt: "2023-11-15",
    },
    {
      id: "quiz-013",
      name: "การดูแลผู้ป่วยโรคผิวหนัง",
      course: "การรักษาโรคทั่วไป",
      courseId: "2",
      lessonId: "lesson-4", // เพิ่ม lessonId
      contentId: "content-12", // เพิ่ม contentId
      questionCount: 14,
      status: "draft",
      createdBy: "ดร.ผิวสวย หนังใส",
      createdAt: "2023-11-18",
    },
    {
      id: "quiz-014",
      name: "การดูแลผู้ป่วยโรคทางเดินอาหาร",
      course: "โภชนาการสำหรับผู้ป่วย",
      courseId: "4",
      lessonId: "lesson-1", // เพิ่ม lessonId
      contentId: "content-5", // เพิ่ม contentId
      questionCount: 18,
      status: "published",
      createdBy: "ดร.ท้องดี อาหารย่อย",
      createdAt: "2023-11-20",
    },
    {
      id: "quiz-015",
      name: "การดูแลผู้ป่วยโรคตับ",
      course: "โภชนาการสำหรับผู้ป่วย",
      courseId: "4",
      lessonId: "lesson-2", // เพิ่ม lessonId
      contentId: "content-7", // เพิ่ม contentId
      questionCount: 16,
      status: "published",
      createdBy: "ดร.ตับดี พักฟื้น",
      createdAt: "2023-11-22",
    },
  ]
  