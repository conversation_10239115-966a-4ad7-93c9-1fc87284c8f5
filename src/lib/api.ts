import axios, { AxiosRequestConfig } from "axios"

// API utility functions for making authenticated requests
const API_BASE_URL = 'http://localhost:8000'

// Get auth token from localStorage
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token')
  }
  return null
}

// Create headers with authentication
const createAuthHeaders = (): Record<string, string> => {
  const token = getAuthToken()
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}

// Generic API request function using axios
export const apiRequest = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {}
): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`
  const headers = createAuthHeaders()

  const config: AxiosRequestConfig = {
    url,
    headers: {
      ...headers,
      ...(options.headers || {}),
    },
    ...options,
  }

  try {
    const response = await axios.request<T>(config)
    return response.data
  } catch (error: any) {
    if (error.response) {
      // Server responded with a status other than 2xx
      throw new Error(error.response.data?.error || `HTTP error! status: ${error.response.status}`)
    } else if (error.request) {
      // Request was made but no response received
      throw new Error("No response from server")
    } else {
      // Something else happened
      throw new Error(error.message)
    }
  }
}

// Convenience methods for different HTTP verbs
export const api = {
  get: <T>(endpoint: string) =>
    apiRequest<T>(endpoint, { method: 'GET' }),

  post: <T>(endpoint: string, data?: any) =>
    apiRequest<T>(endpoint, {
      method: 'POST',
      data,
    }),

  put: <T>(endpoint: string, data?: any) =>
    apiRequest<T>(endpoint, {
      method: 'PUT',
      data,
    }),

  delete: <T>(endpoint: string) =>
    apiRequest<T>(endpoint, { method: 'DELETE' }),
}

// Error handling utility
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}
