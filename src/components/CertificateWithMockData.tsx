"use client"

import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  Award,
  Download,
  Eye,
  Calendar,
  BookOpen,
  ArrowLeft,
  X,
  ExternalLink,
  Target
} from 'lucide-react'

// Mock data types
interface MockUser {
  id: string
  firstname: string
  lastname: string
  email: string
  position: string
  profileImage?: string
}

interface MockCourse {
  id: string
  name: string
  description: string
  coverImage: string
  teacher: {
    name: string
    description: string
  }
  certify: boolean
}

interface MockUserProgress {
  userId: string
  courseId: string
  status: "completed"
  progress: number
  completedLessons: number
  totalLessons: number
  lastAccessed: Date
  certificate: {
    issued: boolean
    date: Date
    url: string
  }
}

interface CertificateWithCourse extends MockUserProgress {
  course: MockCourse
}

// Mock data
const mockUser: MockUser = {
  id: "user-017",
  firstname: "สมชาย",
  lastname: "ใจดี",
  email: "<EMAIL>",
  position: "นักศึกษาแพทย์",
  profileImage: "https://imgcdn.stablediffusionweb.com/2024/9/30/9cde6098-e149-4ee3-9bd7-c671fc585565.jpg"
}

const mockCourses: MockCourse[] = [
  {
    id: "WnH5F0qZF938",
    name: "โรคปอดอักเสบ (Pneumonia)",
    description: "คอร์สนี้ออกแบบมาเพื่อเสริมสร้างความรู้และทักษะด้านโรคปอดอักเสบสำหรับแพทย์ พยาบาล และบุคลากรทางการแพทย์",
    coverImage: "/e-med/img/courses05.jpg",
    teacher: {
      name: "อาจารย์สมชาย",
      description: "ผู้เชี่ยวชาญในการดูแลผู้ป่วย ICU และผู้ป่วยวิกฤต"
    },
    certify: true
  },
  {
    id: "QlH3I647MCuN",
    name: "การให้ยาแก่ผู้ป่วย (Patient Medication)",
    description: "เรียนรู้หลักการและวิธีการให้ยาแก่ผู้ป่วยอย่างปลอดภัยและมีประสิทธิภาพ",
    coverImage: "/e-med/img/courses06.jpg",
    teacher: {
      name: "อาจารย์สมหญิง",
      description: "ผู้เชี่ยวชาญด้านเภสัชกรรมคลินิก"
    },
    certify: true
  },
  {
    id: "19x4uNv4RgED",
    name: "การรักษาโรคอ้วนในเด็ก (Childhood Obesity Treatment)",
    description: "แนวทางการรักษาและการจัดการกับโรคอ้วนในเด็กอย่างมีประสิทธิภาพ",
    coverImage: "/e-med/img/courses01.jpg",
    teacher: {
      name: "อาจารย์วิชัย",
      description: "ผู้เชี่ยวชาญด้านกุมารเวชศาสตร์"
    },
    certify: true
  },
  {
    id: "j0DPSgeg4b2q",
    name: "การดูแลผู้ป่วยโรคซึมเศร้า (Depression Care)",
    description: "ความรู้เกี่ยวกับการดูแลและรักษาผู้ป่วยโรคซึมเศร้าอย่างมีประสิทธิภาพ",
    coverImage: "/e-med/img/courses02.jpg",
    teacher: {
      name: "อาจารย์สุนีย์",
      description: "ผู้เชี่ยวชาญด้านจิตเวชศาสตร์"
    },
    certify: true
  }
]

const mockUserProgress: MockUserProgress[] = [
  {
    userId: "user-017",
    courseId: "WnH5F0qZF938",
    status: "completed",
    progress: 100,
    completedLessons: 2,
    totalLessons: 2,
    lastAccessed: new Date("2023-11-25T14:20:00"),
    certificate: {
      issued: true,
      date: new Date("2023-11-25T15:00:00"),
      url: "/e-med/certificates/Certificate.pdf"
    }
  },
  {
    userId: "user-017",
    courseId: "QlH3I647MCuN",
    status: "completed",
    progress: 100,
    completedLessons: 2,
    totalLessons: 2,
    lastAccessed: new Date("2023-11-12T14:30:00"),
    certificate: {
      issued: true,
      date: new Date("2023-11-26T09:00:00"),
      url: "/e-med/certificates/Certificate.pdf"
    }
  },
  {
    userId: "user-017",
    courseId: "19x4uNv4RgED",
    status: "completed",
    progress: 100,
    completedLessons: 2,
    totalLessons: 2,
    lastAccessed: new Date("2023-11-28T15:30:00"),
    certificate: {
      issued: true,
      date: new Date("2023-11-28T16:00:00"),
      url: "/e-med/certificates/Certificate.pdf"
    }
  },
  {
    userId: "user-017",
    courseId: "j0DPSgeg4b2q",
    status: "completed",
    progress: 100,
    completedLessons: 3,
    totalLessons: 3,
    lastAccessed: new Date("2023-12-01T10:15:00"),
    certificate: {
      issued: true,
      date: new Date("2023-12-01T11:00:00"),
      url: "/e-med/certificates/Certificate.pdf"
    }
  }
]

const CertificateManagement = () => {
  const router = useRouter()
  const [user, setUser] = useState<MockUser | null>(null)
  const [certificates, setCertificates] = useState<CertificateWithCourse[]>([])
  const [selectedCertificate, setSelectedCertificate] = useState<CertificateWithCourse | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [pdfLoadError, setPdfLoadError] = useState(false)
  const [pdfLoading, setPdfLoading] = useState(true)

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => {
      // Set mock user data
      setUser(mockUser)

      // Filter completed courses with certificates for the current user
      const completedWithCertificates = mockUserProgress.filter(
        (progress) => progress.userId === mockUser.id && progress.certificate.issued
      )

      // Combine with course data
      const certificatesWithCourses = completedWithCertificates.map((progress) => {
        const course = mockCourses.find((course) => course.id === progress.courseId)
        return {
          ...progress,
          course: course!
        }
      }).filter((cert) => cert.course) // Filter out any certificates without course data

      setCertificates(certificatesWithCourses)
      setLoading(false)

      // Debug: Log certificate URLs
      console.log('Certificate URLs:', certificatesWithCourses.map(cert => cert.certificate.url))
    }, 1000) // Simulate 1 second loading time

    return () => clearTimeout(timer)
  }, [router])

  const handleViewCertificate = (certificate: CertificateWithCourse) => {
    setSelectedCertificate(certificate)
    setIsModalOpen(true)
    setPdfLoadError(false) // Reset error state when opening modal
    setPdfLoading(true) // Reset loading state when opening modal
  }

  const handleDownloadCertificate = (certificate: CertificateWithCourse) => {
    if (certificate.certificate?.url) {
      // Create a temporary link element to trigger download
      const link = document.createElement('a')
      link.href = certificate.certificate.url
      link.download = `certificate-${certificate.course.name.replace(/[^a-zA-Z0-9]/g, '_')}-${user?.firstname}-${user?.lastname}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f9fafb] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดใบประกาศนียบัตร...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#f9fafb] w-full">
      <div className="flex w-full">

        {/* Main Content */}
        <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
          <div className="w-full p-6">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center mb-2">
                <button
                  onClick={() => router.push("/profile/dashboard")}
                  className="mr-2 p-1 rounded-full hover:bg-gray-200"
                >
                  <ArrowLeft size={20} />
                </button>
                <h1 className="text-2xl font-bold text-gray-800">ใบประกาศนียบัตรของฉัน</h1>
              </div>
              <p className="text-gray-600">
                ใบประกาศนียบัตรที่คุณได้รับจากการเรียนจบคอร์สต่างๆ
              </p>
            </div>

            {/* Certificates Content */}
            {certificates.length === 0 ? (
              <div className="text-center py-16">
                <div className="mb-6">
                  <Award size={64} className="mx-auto text-gray-300" />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  ยังไม่มีใบประกาศนียบัตร
                </h3>
                <p className="text-gray-500 mb-6">
                  เมื่อคุณเรียนจบคอร์สที่มีการออกใบประกาศนียบัตร ใบประกาศนียบัตรจะแสดงที่นี่
                </p>
                <Link
                  href="/courses"
                  className="inline-flex items-center px-6 py-3 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
                >
                  <BookOpen size={20} className="mr-2" />
                  เริ่มเรียนคอร์ส
                </Link>
              </div>
            ) : (
              <>
                {/* Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-[#008268]/10 rounded-lg">
                        <Award className="h-6 w-6 text-[#008268]" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">ใบประกาศนียบัตรทั้งหมด</p>
                        <p className="text-2xl font-bold text-gray-900">{certificates.length}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <BookOpen className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">คอร์สที่เรียนจบ</p>
                        <p className="text-2xl font-bold text-gray-900">{certificates.length}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-green-100 rounded-lg">
                        <Calendar className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">ใบประกาศนียบัตรล่าสุด</p>
                        <p className="text-sm font-bold text-gray-900">
                          {certificates.length > 0 && certificates[0].certificate?.date
                            ? formatDate(certificates[0].certificate.date)
                            : 'ไม่มีข้อมูล'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Certificates Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {certificates.map((certificate) => (
                    <div
                      key={certificate.courseId}
                      className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                    >
                      {/* Certificate Thumbnail */}
                      <div className="relative h-48 bg-gradient-to-br from-[#008268] to-[#006e58] rounded-t-lg flex items-center justify-center">
                        <div className="text-center text-white">
                          <Award size={48} className="mx-auto mb-2" />
                          <p className="text-sm font-medium">ใบประกาศนียบัตร</p>
                        </div>
                        {/* Course Image Overlay */}
                        {certificate.course.coverImage && (
                          <div className="absolute inset-0 bg-black/20 rounded-t-lg">
                            <Image
                              src={certificate.course.coverImage}
                              alt={certificate.course.name}
                              fill
                              className="object-cover rounded-t-lg opacity-30"
                            />
                          </div>
                        )}
                      </div>

                      {/* Certificate Info */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                          {certificate.course.name}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          ผู้สอน: {certificate.course.teacher.name}
                        </p>
                        <p className="text-sm text-gray-500 mb-4">
                          วันที่ออกใบประกาศนียบัตร: {' '}
                          {certificate.certificate?.date
                            ? formatDate(certificate.certificate.date)
                            : 'ไม่มีข้อมูล'
                          }
                        </p>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleViewCertificate(certificate)}
                            className="flex-1 flex items-center justify-center px-3 py-2 bg-[#008268] text-white text-sm rounded-md hover:bg-[#006e58] transition-colors"
                          >
                            <Eye size={16} className="mr-1" />
                            ดู
                          </button>
                          <button
                            onClick={() => handleDownloadCertificate(certificate)}
                            className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                          >
                            <Download size={16} className="mr-1" />
                            ดาวน์โหลด
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Certificate Modal */}
      {isModalOpen && selectedCertificate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  ใบประกาศนียบัตร
                </h3>
                <p className="text-sm text-gray-600">
                  {selectedCertificate.course.name}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleDownloadCertificate(selectedCertificate)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download size={16} className="mr-1" />
                  ดาวน์โหลด
                </button>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-4 overflow-hidden max-h-[calc(90vh-120px)] modal-content">
              {selectedCertificate.certificate?.url ? (
                <div className="w-full overflow-hidden">
                  {/* PDF Viewer for Desktop */}
                  <div className="hidden md:block">
                    {!pdfLoadError ? (
                      <div className="relative w-full h-[605px] hide-scrollbar">
                        <iframe
                          src={`${selectedCertificate.certificate.url}#toolbar=0&navpanes=0&scrollbar=0`}
                          className="w-full h-full border"
                          title="Certificate PDF"
                          onError={() => {
                            setPdfLoadError(true);
                            setPdfLoading(false);
                          }}
                          onLoad={(e) => {
                            setPdfLoading(false);
                            // Check if iframe loaded successfully
                            const iframe = e.target as HTMLIFrameElement;
                            try {
                              // Small delay to ensure content is loaded
                              setTimeout(() => {
                                if (iframe.contentDocument === null) {
                                  setPdfLoadError(true);
                                }
                              }, 1000);
                            } catch (error) {
                              setPdfLoadError(true);
                            }
                          }}
                        />
                        {/* Loading overlay */}
                        {pdfLoading && (
                          <div className="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-lg">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#008268] mx-auto mb-2"></div>
                              <p className="text-sm text-gray-600">กำลังโหลดใบประกาศนียบัตร...</p>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      /* Fallback when iframe fails */
                      <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                        <Award size={64} className="mx-auto text-[#008268] mb-4" />
                        <h4 className="text-lg font-semibold mb-2 text-gray-900">
                          ใบประกาศนียบัตร
                        </h4>
                        <p className="text-gray-600 mb-2">
                          {selectedCertificate.course.name}
                        </p>
                        <p className="text-gray-600 mb-4">
                          ออกให้แก่: {user?.firstname} {user?.lastname}
                        </p>
                        <p className="text-sm text-gray-500 mb-6">
                          วันที่: {selectedCertificate.certificate?.date
                            ? formatDate(selectedCertificate.certificate.date)
                            : 'ไม่มีข้อมูล'
                          }
                        </p>
                        <div className="flex flex-col sm:flex-row gap-3 justify-center">
                          <a
                            href={selectedCertificate.certificate.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                          >
                            <ExternalLink size={16} className="mr-2" />
                            เปิดในแท็บใหม่
                          </a>
                          <button
                            onClick={() => handleDownloadCertificate(selectedCertificate)}
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                          >
                            <Download size={16} className="mr-2" />
                            ดาวน์โหลด PDF
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Mobile View */}
                  <div className="md:hidden text-center py-8">
                    <Award size={64} className="mx-auto text-[#008268] mb-4" />
                    <h4 className="text-lg font-semibold mb-2">
                      {selectedCertificate.course.name}
                    </h4>
                    <p className="text-gray-600 mb-4">
                      ออกให้แก่: {user?.firstname} {user?.lastname}
                    </p>
                    <p className="text-sm text-gray-500 mb-6">
                      วันที่: {selectedCertificate.certificate?.date
                        ? formatDate(selectedCertificate.certificate.date)
                        : 'ไม่มีข้อมูล'
                      }
                    </p>
                    <div className="flex flex-col gap-3">
                      <a
                        href={selectedCertificate.certificate.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center justify-center px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                      >
                        <ExternalLink size={16} className="mr-2" />
                        เปิดใบประกาศนียบัตร
                      </a>
                      <button
                        onClick={() => handleDownloadCertificate(selectedCertificate)}
                        className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        <Download size={16} className="mr-2" />
                        ดาวน์โหลด PDF
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">ไม่พบไฟล์ใบประกาศนียบัตร</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Custom CSS for hiding scrollbar */}
      <style jsx global>{`
        /* Hide scrollbar for webkit browsers (Chrome, Safari, Edge) */
        .hide-scrollbar::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
          height: 0 !important;
        }

        /* Hide scrollbar for Firefox */
        .hide-scrollbar {
          -ms-overflow-style: none !important;  /* Internet Explorer 10+ */
          scrollbar-width: none !important;     /* Firefox */
        }

        /* Additional iframe styling to ensure no scrollbars */
        .hide-scrollbar iframe {
          overflow: hidden !important;
          border: none;
        }

        /* Ensure modal content has no scrollbars */
        .modal-content {
          overflow: hidden !important;
        }

        /* Force hide any potential scrollbars in PDF containers */
        iframe[src*="pdf"], iframe[src*="Certificate"] {
          overflow: hidden !important;
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
        }

        iframe[src*="pdf"]::-webkit-scrollbar,
        iframe[src*="Certificate"]::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
          height: 0 !important;
        }
      `}</style>
    </div>
  )
}

export default CertificateManagement
