"use client"

import type React from "react"
import { useEffect, useState } from "react"
import Link from "next/link"
import { Clock, Route } from "lucide-react"

import { learningPathsData } from "@/data/learningPaths"
import { getPathsAssignedToUser } from "@/data/assignedPaths"
import { getUserProgress } from "@/data/userProgress"

// Types matching the dashboard component
interface DashboardCourse {
  id: string
  name: string
  isCompleted: boolean
  isUnlocked: boolean
}

interface DashboardPathway {
  id: string
  name: string
  description: string
  difficulty: "beginner" | "intermediate" | "advanced"
  estimatedDuration: number
  courses: DashboardCourse[]
  completedCourses: number
  totalCourses: number
  progressPercentage: number
  status: "not_started" | "in_progress" | "completed"
}

interface PathPreviewCardProps {
  userId?: string
  maxItems?: number
}

const PathPreviewCard: React.FC<PathPreviewCardProps> = ({ userId, maxItems = 3 }) => {
  const [userPathways, setUserPathways] = useState<DashboardPathway[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadUserPathways = () => {
      try {
        // Get current user ID
        const currentUserId = userId || (typeof window !== "undefined" ? localStorage.getItem("userId") : null)

        if (!currentUserId) {
          setLoading(false)
          return
        }

        console.log("Loading pathways for user:", currentUserId) // Debug log

        // Get paths assigned to this user using the helper function
        const assignedPathIds = getPathsAssignedToUser(currentUserId)
        console.log("Assigned path IDs:", assignedPathIds) // Debug log

        // Filter only assigned paths
        const assignedPaths = learningPathsData.filter((path) => assignedPathIds.includes(path.id))
        console.log("Found assigned paths:", assignedPaths) // Debug log

        // Get user progress using the function
        const userProgress = getUserProgress(currentUserId)
        console.log("User progress:", userProgress) // Debug log

        // Convert to dashboard pathway format
        const pathsWithStats: DashboardPathway[] = assignedPaths.map((path) => {
          // Map courses with completion status
          const courses: DashboardCourse[] = path.courseIds.map((courseId, index) => {
            const progress = userProgress.find((p) => p.courseId === courseId)
            return {
              id: courseId,
              name: `คอร์ส ${courseId}`, // You can map this to actual course names
              isCompleted: progress?.status === "completed",
              isUnlocked:
                index === 0 ||
                userProgress.some(
                  (p) => path.courseIds.slice(0, index).includes(p.courseId) && p.status === "completed",
                ),
            }
          })

          // Count completed courses
          const completedCourses = courses.filter((c) => c.isCompleted).length

          // Calculate progress percentage
          const progressPercentage = Math.round((completedCourses / courses.length) * 100)

          // Determine status
          let status: "not_started" | "in_progress" | "completed" = "not_started"
          if (progressPercentage === 100) {
            status = "completed"
          } else if (progressPercentage > 0) {
            status = "in_progress"
          }

          // Auto-detect specialty and difficulty
          const difficulty: "beginner" | "intermediate" | "advanced" =
            courses.length <= 2 ? "beginner" : courses.length <= 4 ? "intermediate" : "advanced"

          return {
            id: path.id,
            name: path.name,
            description: path.description,
            difficulty,
            estimatedDuration: path.estimatedDuration || courses.length * 4, // Default 4 hours per course
            courses,
            completedCourses,
            totalCourses: courses.length,
            progressPercentage,
            status,
          }
        })

        console.log("Final pathways with stats:", pathsWithStats) // Debug log

        // Limit to maxItems and sort by progress (in_progress first, then not_started, then completed)
        const sortedPaths = pathsWithStats
          .sort((a, b) => {
            const statusOrder = { in_progress: 0, not_started: 1, completed: 2 }
            return statusOrder[a.status] - statusOrder[b.status]
          })
          .slice(0, maxItems)

        setUserPathways(sortedPaths)
        setLoading(false)
      } catch (error) {
        console.error("Error loading pathways:", error)
        setLoading(false)
      }
    }

    loadUserPathways()
  }, [userId, maxItems])

  const getProgressColor = (percentage: number) => {
    if (percentage === 0) return "bg-gray-300"
    if (percentage <= 25) return "bg-red-500"
    if (percentage <= 50) return "bg-orange-500"
    if (percentage <= 75) return "bg-yellow-500"
    return "bg-green-500"
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg p-4 shadow-sm border animate-pulse">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded mb-2 w-full"></div>
                <div className="flex gap-2 mb-2">
                  <div className="h-5 bg-gray-200 rounded w-16"></div>
                  <div className="h-5 bg-gray-200 rounded w-12"></div>
                </div>
              </div>
              <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
            </div>
            <div className="mb-3">
              <div className="h-2 bg-gray-200 rounded mb-1"></div>
              <div className="h-1.5 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (userPathways.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mb-4">
          <Route size={48} className="mx-auto text-gray-300" />
        </div>
        <h3 className="text-lg font-semibold text-gray-600 mb-2">ยังไม่มีเส้นทางการเรียน</h3>
        <p className="text-gray-500 mb-4">กรุณาติดต่อผู้ดูแลระบบเพื่อขอรับมอบหมายเส้นทางการเรียน</p>
        <Link
          href="/courses"
          className="inline-flex items-center px-4 py-2 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
        >
          ดูคอร์สทั้งหมด
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {userPathways.map((pathway) => (
        <div
          key={pathway.id}
          className="bg-white rounded-lg p-4 shadow-sm border hover:shadow-md transition-shadow duration-200"
        >
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm mb-1">{pathway.name}</h3>
              <p className="text-xs text-gray-600 mb-2 line-clamp-2">{pathway.description}</p>
              <div className="flex items-center gap-2">
                <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800">
                  {pathway.totalCourses} คอร์ส
                </span>
                <span className="text-xs text-gray-500 flex items-center">
                  <Clock size={10} className="mr-1" />
                  {pathway.estimatedDuration} ชม.
                </span>
              </div>
            </div>
            <div className="ml-3">
              <div className="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                <Route size={20} className="text-white" />
              </div>
            </div>
          </div>

          {/* Progress */}
          <div className="mb-3">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>ความก้าวหน้า</span>
              <span className="text-gray-500">
                {pathway.completedCourses} จาก {pathway.totalCourses} คอร์ส
              </span>
            </div>
            <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-300 ${getProgressColor(pathway.progressPercentage)}`}
                style={{ width: `${pathway.progressPercentage}%` }}
              ></div>
            </div>
            <div className="text-right text-xs text-gray-500 mt-1">{pathway.progressPercentage}%</div>
          </div>
        </div>
      ))}

      {/* View All Link */}
      {userPathways.length > 0 && (
        <div className="text-center pt-2">
          <Link href="/profile/certificate-pathways" className="text-sm text-[#008268] hover:text-[#006e58] font-medium">
            ดูเส้นทางการเรียนทั้งหมด →
          </Link>
        </div>
      )}
    </div>
  )
}

export default PathPreviewCard
