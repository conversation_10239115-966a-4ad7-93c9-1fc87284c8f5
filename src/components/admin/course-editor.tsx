"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { Upload, ChevronDown, Plus, Trash2, Save, AlertTriangle, X, GripVertical } from "lucide-react"
import { userService, courseService, type User, type CreateCourseRequest } from "@/services/courseService"
import {
  showSaveConfirmDialog,
  showCourseSaveSuccess,
  showCourseSaveError,
  showLessonDeleteConfirm,
  showContentDeleteConfirm,
  showValidationError,
  showLoadingAlert,
  closeLoadingAlert
} from "@/lib/sweetAlert"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"

// Utility function to generate unique IDs
const generateUniqueId = (prefix: string) => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
}
import {
  array<PERSON><PERSON>,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

// แก้ไขโครงสร้างข้อมูล Lesson และเพิ่ม Content
type LessonType = "text" | "video"

interface Content {
  id: string
  name: string
  typecontent: string
  details: string
  time: number
}

interface Lesson {
  id: string
  name: string
  description: string
  time: number
  content: Content[]
}

interface CourseFormData {
  title: string
  lecturer: string
  description: string
  moduleDescription: string
  difficulty: string
  duration: number
  status: string
  coverImage: ArrayBuffer | null // changed from string | null
  certify: boolean
  lessons: Lesson[]
}

// แก้ไขฟังก์ชัน SortableLesson เพื่อให้ทำงานกับโครงสร้างข้อมูลใหม่
function SortableLesson({
  lesson,
  index,
  isSelected,
  onSelect,
}: {
  lesson: Lesson
  index: number
  isSelected: boolean
  onSelect: () => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: lesson.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`p-3 rounded-md cursor-pointer mb-2 ${isSelected ? "bg-[#E6F2F0] border border-[#008268]" : "bg-white border border-gray-200"
        } ${isDragging ? "shadow-md" : ""}`}
    >
      <div className="flex items-center">
        <div
          {...attributes}
          {...listeners}
          className="mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
        >
          <GripVertical size={16} />
        </div>
        <span className="text-sm font-medium mr-2">{index + 1}.</span>
        <div className="flex-1 text-sm truncate" onClick={onSelect}>
          {lesson.name ? lesson.name : "บทเรียนไม่มีชื่อ"}
        </div>
      </div>
    </div>
  )
}

export default function CourseEditor({ courseSlug }: { courseSlug?: string }) {
  const [activeTab, setActiveTab] = useState<"course" | "lessons">("course")
  const [selectedLesson, setSelectedLesson] = useState<number>(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // API state management
  const [lecturers, setLecturers] = useState<User[]>([])
  const [isLoadingLecturers, setIsLoadingLecturers] = useState(true)
  const [isCreatingCourse, setIsCreatingCourse] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)
  const [isLoadingCourse, setIsLoadingCourse] = useState(false)
  const [existingCourse, setExistingCourse] = useState<any>(null)

  // For previewing ArrayBuffer as image
  const [coverImageUrl, setCoverImageUrl] = useState<string | null>(null)

  // Determine if we're in edit mode
  const isEditMode = Boolean(courseSlug)

  // Function to populate form with existing course data
  const populateFormWithCourseData = (courseData: any) => {
    // Find the lecturer slug from the lecturer ID
    const lecturerSlug = lecturers.find(lecturer =>
      lecturer.id === courseData.user_id
    )?.user_slug || ""

    // Convert course data to form format
    const populatedFormData: CourseFormData = {
      title: courseData.course_name || "",
      lecturer: lecturerSlug,
      description: courseData.course_description || "",
      moduleDescription: courseData.course_instruction || "",
      difficulty: courseData.course_difficulty || "ปานกลาง",
      duration: parseInt(courseData.course_duration) || 4,
      status: courseData.course_status ? "published" : "draft",
      coverImage: null, // Will be handled separately for image display
      certify: courseData.course_certificate || false,
      lessons: courseData.lessons || defaultLessons,
    }

    setFormData(populatedFormData)

    // Handle cover image if exists
    if (courseData.course_picture) {
      setCoverImageUrl(`data:image/jpeg;base64,${courseData.course_picture}`)
    }
  }

  // Fetch lecturers and course data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoadingLecturers(true)
        if (isEditMode) {
          setIsLoadingCourse(true)
        }

        // Fetch lecturers
        const fetchedLecturers = await userService.getLecturers()
        setLecturers(fetchedLecturers)

        // Fetch course data if in edit mode
        if (isEditMode && courseSlug) {
          try {
            const courseData = await courseService.getCourseBySlug(courseSlug)
            setExistingCourse(courseData)

            // Populate form with existing course data
            populateFormWithCourseData(courseData)
          } catch (courseError) {
            console.error('Failed to fetch course data:', courseError)
            // For now, show a specific error for missing backend implementation
            setApiError('ฟีเจอร์การแก้ไขคอร์สยังไม่พร้อมใช้งาน (Backend API ยังไม่ได้ implement)')
          }
        }

        setApiError(null)
      } catch (error) {
        console.error('Failed to fetch data:', error)
        setApiError(isEditMode ? 'ไม่สามารถโหลดข้อมูลคอร์สได้' : 'ไม่สามารถโหลดข้อมูลอาจารย์ได้')
      } finally {
        setIsLoadingLecturers(false)
        if (isEditMode) {
          setIsLoadingCourse(false)
        }
      }
    }

    fetchData()
  }, [courseSlug, isEditMode])

  // Default lesson structure for new courses
  const defaultLessons: Lesson[] = [
    {
      id: generateUniqueId('lesson'),
      name: "",
      description: "",
      time: 1800, // 30 นาที
      content: [
        {
          id: generateUniqueId('content'),
          name: "เนื้อหาบทเรียน",
          typecontent: "text",
          details: "",
          time: 1800,
        },
      ],
    },
  ]

  // Initial form data for new course creation
  const initialFormData: CourseFormData = {
    title: "",
    lecturer: "",
    description: "",
    moduleDescription:
      "คุณจะได้ศึกษาเชิงลึกเกี่ยวกับหัวข้อสำคัญและหลักการพื้นฐานในหลักสูตรนี้ ผ่านชุดโมดูลที่ออกแบบมาเพื่อเสริมสร้างความเข้าใจอย่างเป็นระบบ เพื่อให้คุณสามารถนำความรู้ไปประยุกต์ใช้ได้อย่างมีประสิทธิภาพ",
    difficulty: "ปานกลาง",
    duration: 4,
    status: "draft",
    coverImage: null,
    certify: false,
    lessons: defaultLessons,
  }

  const [formData, setFormData] = useState<CourseFormData>(initialFormData)

  // Remove old modal states - now using SweetAlert2

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  // แก้ไขฟังก์ชัน handleLessonInputChange เพื่อให้ทำงานกับโครงสร้างข้อมูลใหม่
  const handleLessonInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    index: number,
  ) => {
    const { name, value } = e.target
    const updatedLessons = [...formData.lessons]

    // ถ้าเป็นการเปลี่ยนแปลงเวลา ให้แปลงเป็นตัวเลข
    if (name === "time") {
      updatedLessons[index] = {
        ...updatedLessons[index],
        [name]: Number.parseInt(value, 10) || 0,
      }
    } else {
      updatedLessons[index] = {
        ...updatedLessons[index],
        [name]: value,
      }
    }

    setFormData((prev) => ({
      ...prev,
      lessons: updatedLessons,
    }))
  }

  // เพิ่มฟังก์ชันสำหรับจัดการการเปลี่ยนแปลงข้อมูลเนื้อหาบทเรียน
  const handleContentInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    lessonIndex: number,
    contentIndex: number,
  ) => {
    const { name, value } = e.target
    const updatedLessons = [...formData.lessons]
    const updatedContents = [...updatedLessons[lessonIndex].content]

    // ถ้าเป็นการเปลี่ยนแปลงเวลา ให้แปลงเป็นตัวเลข
    if (name === "time") {
      updatedContents[contentIndex] = {
        ...updatedContents[contentIndex],
        [name]: Number.parseInt(value, 10) || 0,
      }
    } else {
      updatedContents[contentIndex] = {
        ...updatedContents[contentIndex],
        [name]: value,
      }
    }

    updatedLessons[lessonIndex] = {
      ...updatedLessons[lessonIndex],
      content: updatedContents,
    }

    setFormData((prev) => ({
      ...prev,
      lessons: updatedLessons,
    }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        const arrayBuffer = event.target?.result as ArrayBuffer
        setFormData((prev) => ({
          ...prev,
          coverImage: arrayBuffer,
        }))
        // Create a blob URL for preview
        const blob = new Blob([arrayBuffer], { type: file.type })
        const url = URL.createObjectURL(blob)
        setCoverImageUrl(url)
        // Log the byte length for debug
        if (arrayBuffer instanceof ArrayBuffer) {
          console.log("coverImage bytes length:", arrayBuffer.byteLength)
        }
      }
      reader.readAsArrayBuffer(file)
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  // แก้ไขฟังก์ชัน addNewLesson เพื่อให้ตรงกับโครงสร้างข้อมูลใหม่
  const addNewLesson = () => {
    const newLesson: Lesson = {
      id: generateUniqueId('lesson'),
      name: "",
      description: "",
      time: 1800, // 30 นาที
      content: [
        {
          id: generateUniqueId('content'),
          name: "เนื้อหาบทเรียน",
          typecontent: "text",
          details: "",
          time: 1800,
        },
      ],
    }
    setFormData((prev) => ({
      ...prev,
      lessons: [...prev.lessons, newLesson],
    }))
    setSelectedLesson(formData.lessons.length)
  }

  // เพิ่มฟังก์ชันสำหรับเพิ่มเนื้อหาบทเรียนใหม่
  const addNewContent = (lessonIndex: number) => {
    const updatedLessons = [...formData.lessons]
    const newContent: Content = {
      id: generateUniqueId('content'),
      name: "เนื้อหาบทเรียนใหม่",
      typecontent: "text",
      details: "",
      time: 1800, // 30 นาที
    }

    updatedLessons[lessonIndex] = {
      ...updatedLessons[lessonIndex],
      content: [...updatedLessons[lessonIndex].content, newContent],
    }

    setFormData((prev) => ({
      ...prev,
      lessons: updatedLessons,
    }))
  }

  // เพิ่มฟังก์ชันสำหรับลบเนื้อหาบทเรียน ให้ใช้ SweetAlert2
  const deleteContent = async (lessonIndex: number, contentIndex: number) => {
    if (formData.lessons[lessonIndex].content.length <= 1) {
      showValidationError("บทเรียนต้องมีเนื้อหาอย่างน้อย 1 รายการ")
      return
    }

    const contentName = formData.lessons[lessonIndex].content[contentIndex]?.name || `เนื้อหาที่ ${contentIndex + 1}`
    const result = await showContentDeleteConfirm(contentName)

    if (result.isConfirmed) {
      const updatedLessons = [...formData.lessons]
      updatedLessons[lessonIndex].content = updatedLessons[lessonIndex].content.filter((_, i) => i !== contentIndex)

      setFormData((prev) => ({
        ...prev,
        lessons: updatedLessons,
      }))
    }
  }

  // แก้ไขฟังก์ชัน deleteLesson ให้ใช้ SweetAlert2
  const confirmDeleteLesson = async (index: number) => {
    if (formData.lessons.length <= 1) {
      showValidationError('ต้องมีอย่างน้อย 1 บทเรียนในคอร์ส')
      return
    }

    const lessonName = formData.lessons[index]?.name || `บทเรียนที่ ${index + 1}`
    const result = await showLessonDeleteConfirm(lessonName)

    if (result.isConfirmed) {
      deleteLesson(index)
    }
  }

  const deleteLesson = (index: number) => {
    const updatedLessons = formData.lessons.filter((_, i) => i !== index)
    setFormData((prev) => ({
      ...prev,
      lessons: updatedLessons,
    }))

    if (selectedLesson >= updatedLessons.length) {
      setSelectedLesson(updatedLessons.length - 1)
    } else if (selectedLesson === index) {
      setSelectedLesson(Math.max(0, index - 1))
    }
  }

  const saveLesson = async (index: number) => {
    console.log("Saving lesson:", formData.lessons[index])
    // API call to save the lesson would go here

    // Show success notification
    await showCourseSaveSuccess(false)
  }

  const saveAllChanges = async () => {
    // Validation
    if (!formData.title.trim()) {
      showValidationError('กรุณากรอกชื่อคอร์สเรียน')
      return
    }

    if (!formData.lecturer) {
      showValidationError('กรุณาเลือกอาจารย์ผู้สอน')
      return
    }

    // Show confirmation dialog
    const result = await showSaveConfirmDialog(isEditMode)
    if (!result.isConfirmed) {
      return
    }

    try {
      setIsCreatingCourse(true)
      setApiError(null)

      // Show loading dialog
      showLoadingAlert(isEditMode ? 'กำลังอัปเดตคอร์ส...' : 'กำลังบันทึกคอร์ส...')

      // Find the selected lecturer to validate they exist
      const selectedLecturer = lecturers.find(lecturer => lecturer.user_slug === formData.lecturer)
      if (!selectedLecturer) {
        closeLoadingAlert()
        throw new Error('ไม่พบข้อมูลอาจารย์ที่เลือก')
      }

      // Convert ArrayBuffer to base64 string if present
      let coverImageString: string | null = null
      if (formData.coverImage instanceof ArrayBuffer) {
        const uint8Array = new Uint8Array(formData.coverImage)
        const binaryString = uint8Array.reduce((data, byte) => data + String.fromCharCode(byte), "")
        coverImageString = btoa(binaryString)
      }

      // Transform lessons and content to remove id fields for backend
      const lessonsForApi = formData.lessons.map(lesson => ({
        name: lesson.name,
        description: lesson.description,
        time: lesson.time,
        content: lesson.content.map(content => ({
          name: content.name,
          typecontent: content.typecontent,
          details: content.details,
          time: content.time,
        }))
      }))

      // Prepare course data for API
      const courseData: CreateCourseRequest = {
        title: formData.title,
        lecturer: formData.lecturer,
        description: formData.description,
        moduleDescription: formData.moduleDescription,
        difficulty: formData.difficulty,
        duration: formData.duration,
        status: formData.status === "published", // Convert string to boolean
        coverImage: coverImageString || "", // send as base64 or empty string
        certify: formData.certify,
        lessons: lessonsForApi,
      }

      let response
      if (isEditMode && courseSlug) {
        console.log("Updating course with data:", courseData)
        response = await courseService.updateCourse(courseSlug, courseData)
        console.log("Course updated successfully:", response)
      } else {
        console.log("Creating course with data:", courseData)
        response = await courseService.createCourse(courseData)
        console.log("Course created successfully:", response)
      }

      // Close loading and show success
      closeLoadingAlert()
      await showCourseSaveSuccess(isEditMode)
    } catch (error) {
      console.error(`Failed to ${isEditMode ? 'update' : 'create'} course:`, error)
      closeLoadingAlert()
      const errorMessage = error instanceof Error ? error.message : undefined
      await showCourseSaveError(isEditMode, errorMessage)
      setApiError(error instanceof Error ? error.message : `เกิดข้อผิดพลาดในการ${isEditMode ? 'แก้ไข' : 'สร้าง'}คอร์สเรียน`)
    } finally {
      setIsCreatingCourse(false)
    }
  }

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      setFormData((prev) => {
        // Find the indices of the dragged item and the drop target
        const oldIndex = prev.lessons.findIndex((lesson) => lesson.id === active.id)
        const newIndex = prev.lessons.findIndex((lesson) => lesson.id === over.id)

        // Update selectedLesson to follow the dragged item
        if (selectedLesson === oldIndex) {
          setSelectedLesson(newIndex)
        } else if (selectedLesson >= newIndex && selectedLesson < oldIndex) {
          setSelectedLesson(selectedLesson + 1)
        } else if (selectedLesson <= newIndex && selectedLesson > oldIndex) {
          setSelectedLesson(selectedLesson - 1)
        }

        return {
          ...prev,
          lessons: arrayMove(prev.lessons, oldIndex, newIndex),
        }
      })
    }
  }

  // เพิ่ม state สำหรับเลือกเนื้อหาบทเรียน
  const [selectedContent, setSelectedContent] = useState<number>(0)

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with save button */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">{isEditMode ? "แก้ไขคอร์สเรียน" : "สร้างคอร์สเรียนใหม่"}</h1>
        <div className="flex items-center gap-3">
          {apiError && (
            <div className="bg-red-50 text-red-700 px-3 py-1 rounded-md flex items-center">
              <AlertTriangle className="w-4 h-4 mr-1" />
              {apiError}
            </div>
          )}
          <button
            onClick={saveAllChanges}
            disabled={isCreatingCourse || isLoadingLecturers || isLoadingCourse}
            className="bg-black hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md font-medium flex items-center gap-2"
          >
            {isCreatingCourse ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {isEditMode ? 'กำลังอัปเดต...' : 'กำลังบันทึก...'}
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {isEditMode ? 'อัปเดต' : 'บันทึก'}
              </>
            )}
          </button>
        </div>
      </div>

      {/* Navigation tabs */}
      <div className="flex border-b border-gray-200">
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${activeTab === "course" ? "text-[#008268] border-b-2 border-[#008268]" : "text-gray-500 hover:text-gray-700"
            }`}
          onClick={() => setActiveTab("course")}
        >
          คอร์สเรียน
        </button>
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${activeTab === "lessons" ? "text-[#008268] border-b-2 border-[#008268]" : "text-gray-500 hover:text-gray-700"
            }`}
          onClick={() => setActiveTab("lessons")}
        >
          บทเรียน
        </button>
      </div>

      {/* Loading state for course data */}
      {isLoadingCourse && (
        <div className="p-6 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#008268]"></div>
            <span className="text-gray-600">กำลังโหลดข้อมูลคอร์ส...</span>
          </div>
        </div>
      )}

      {/* Course form */}
      {activeTab === "course" && !isLoadingCourse && (
        <div className="p-6">
          {/* Course cover image - full width at the top */}
          <div className="mb-6">
            <input type="file" ref={fileInputRef} onChange={handleImageUpload} className="hidden" accept="image/*" />
            <div
              onClick={triggerFileInput}
              className="relative w-full h-64 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
            >
              {coverImageUrl ? (
                <div className="relative w-full h-full">
                  <Image
                    src={coverImageUrl}
                    alt="Course cover"
                    fill
                    className="object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                    <div className="bg-white p-2 rounded-full">
                      <Upload className="h-5 w-5 text-gray-700" />
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <Upload className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500">คลิกเพื่ออัพโหลดรูปปกคอร์สเรียน</p>
                </>
              )}
            </div>
          </div>

          {/* Two columns for details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - Course details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดของคอร์สเรียน</h3>

                <div className="mb-4">
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                    ชื่อคอร์สเรียน
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกชื่อคอร์สเรียน"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="lecturer" className="block text-sm font-medium text-gray-700 mb-1">
                    อาจารย์ผู้สอน
                  </label>
                  <div className="relative">
                    <select
                      id="lecturer"
                      name="lecturer"
                      value={formData.lecturer}
                      onChange={handleInputChange}
                      disabled={isLoadingLecturers}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10 disabled:opacity-50"
                    >
                      <option value="">
                        {isLoadingLecturers ? "กำลังโหลด..." : "เลือกอาจารย์ผู้สอน"}
                      </option>
                      {lecturers.map((lecturer, index) => (
                        <option key={`lecturer-${lecturer.id}-${index}`} value={lecturer.user_slug}>
                          {lecturer.user_fname} {lecturer.user_lname}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  {isLoadingLecturers && (
                    <p className="text-xs text-gray-500 mt-1">กำลังโหลดข้อมูลอาจารย์...</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    คำอธิบายคอร์สเรียน
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={5}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำอธิบายคอร์สเรียน"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="moduleDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    บรรยายโมดูล
                  </label>
                  <textarea
                    id="moduleDescription"
                    name="moduleDescription"
                    value={formData.moduleDescription}
                    onChange={handleInputChange}
                    rows={5}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกบรรยายโมดูล"
                  />
                </div>
              </div>
            </div>

            {/* Right column - Additional details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดเพิ่มเติม</h3>
                <div className="mb-4">
                  <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700 mb-1">
                    ระดับความยากง่าย
                  </label>
                  <div className="relative">
                    <select
                      id="difficulty"
                      name="difficulty"
                      value={formData.difficulty}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="ง่าย">ง่าย</option>
                      <option value="ปานกลาง">ปานกลาง</option>
                      <option value="ยาก">ยาก</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                    ระยะเวลาเรียน (ชั่วโมง)
                  </label>
                  <input
                    type="number"
                    id="duration"
                    name="duration"
                    min="1"
                    max="100"
                    value={formData.duration}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                    สถานะคอร์สเรียน
                  </label>
                  <div className="relative">
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="draft">แบบร่าง</option>
                      <option value="published">เผยแพร่</option>
                      <option value="archived">เก็บถาวร</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="certify"
                      name="certify"
                      checked={formData.certify}
                      onChange={(e) => setFormData((prev) => ({ ...prev, certify: e.target.checked }))}
                      className="h-4 w-4 text-[#008268] focus:ring-[#008268] border-gray-300 rounded"
                    />
                    <label htmlFor="certify" className="ml-2 block text-sm font-medium text-gray-700">
                      มีใบรับรองเมื่อเรียนจบ
                    </label>
                  </div>
                  <p className="text-xs text-gray-500 mt-1 ml-6">ผู้เรียนจะได้รับใบรับรองเมื่อเรียนจบคอร์สนี้</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Lessons form */}
      {activeTab === "lessons" && (
        <div className="flex h-[calc(100vh-220px)] min-h-[500px]">
          {/* Left sidebar - Lesson list with drag and drop */}
          <div className="w-full md:w-1/4 lg:w-3/10 border-r border-gray-200 bg-gray-50 p-4 overflow-y-auto">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext
                items={formData.lessons.map((lesson) => lesson.id)}
                strategy={verticalListSortingStrategy}
              >
                {formData.lessons.map((lesson, index) => (
                  <SortableLesson
                    key={lesson.id}
                    lesson={lesson}
                    index={index}
                    isSelected={selectedLesson === index}
                    onSelect={() => {
                      setSelectedLesson(index)
                      setSelectedContent(0) // รีเซ็ตเนื้อหาที่เลือกเมื่อเปลี่ยนบทเรียน
                    }}
                  />
                ))}
              </SortableContext>
            </DndContext>

            <button
              onClick={addNewLesson}
              className="mt-4 w-full flex items-center justify-center p-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-1" />
              <span className="text-sm">เพิ่มบทเรียน</span>
            </button>
          </div>

          {/* Right content - Lesson editor */}
          <div className="w-full md:w-3/4 lg:w-7/10 p-6 overflow-y-auto">
            {formData.lessons.length > 0 && (
              <div>
                <div className="mb-4">
                  <label htmlFor="lessonName" className="block text-sm font-medium text-gray-700 mb-1">
                    ชื่อบทเรียน
                  </label>
                  <input
                    type="text"
                    id="lessonName"
                    name="name"
                    value={formData.lessons[selectedLesson].name}
                    onChange={(e) => handleLessonInputChange(e, selectedLesson)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกชื่อบทเรียน"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="lessonDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    บรรยายบทเรียน
                  </label>
                  <textarea
                    id="lessonDescription"
                    name="description"
                    value={formData.lessons[selectedLesson].description}
                    onChange={(e) => handleLessonInputChange(e, selectedLesson)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกบรรยายบทเรียน"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="lessonTime" className="block text-sm font-medium text-gray-700 mb-1">
                    ระยะเวลาบทเรียน (วินาที)
                  </label>
                  <input
                    type="number"
                    id="lessonTime"
                    name="time"
                    value={formData.lessons[selectedLesson].time}
                    onChange={(e) => handleLessonInputChange(e, selectedLesson)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกระยะเวลาบทเรียน (วินาที)"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {Math.floor(formData.lessons[selectedLesson].time / 3600)} ชั่วโมง{" "}
                    {Math.floor((formData.lessons[selectedLesson].time % 3600) / 60)} นาที
                  </p>
                </div>

                <div className="mt-6 mb-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium text-gray-700">เนื้อหาบทเรียน</h3>
                    <button
                      onClick={() => addNewContent(selectedLesson)}
                      className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md flex items-center"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      เพิ่มเนื้อหา
                    </button>
                  </div>

                  <div className="mt-3 border border-gray-200 rounded-md overflow-hidden">
                    {/* Tab สำหรับเลือกเนื้อหา */}
                    <div className="flex border-b border-gray-200 bg-gray-50">
                      {formData.lessons[selectedLesson].content.map((content, idx) => (
                        <button
                          key={content.id}
                          type="button"
                          className={`px-4 py-2 text-sm ${selectedContent === idx
                            ? "bg-white border-b-2 border-[#008268] text-[#008268] font-medium"
                            : "text-gray-600 hover:bg-gray-100"
                            }`}
                          onClick={() => setSelectedContent(idx)}
                        >
                          {content.name || `เนื้อหา ${idx + 1}`}
                        </button>
                      ))}
                    </div>

                    {/* แบบฟอร์มแก้ไขเนื้อหา */}
                    <div className="p-4 bg-white">
                      <div className="mb-4">
                        <label htmlFor="contentName" className="block text-sm font-medium text-gray-700 mb-1">
                          ชื่อเนื้อหา
                        </label>
                        <input
                          type="text"
                          id="contentName"
                          name="name"
                          value={formData.lessons[selectedLesson].content[selectedContent].name}
                          onChange={(e) => handleContentInputChange(e, selectedLesson, selectedContent)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                          placeholder="กรอกชื่อเนื้อหา"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="contentType" className="block text-sm font-medium text-gray-700 mb-1">
                          ประเภทเนื้อหา
                        </label>
                        <div className="relative">
                          <select
                            id="contentType"
                            name="typecontent"
                            value={formData.lessons[selectedLesson].content[selectedContent].typecontent}
                            onChange={(e) => handleContentInputChange(e, selectedLesson, selectedContent)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                          >
                            <option value="text">ข้อความ</option>
                            <option value="video">วิดีโอ</option>
                          </select>
                          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                            <ChevronDown className="h-4 w-4 text-gray-500" />
                          </div>
                        </div>
                      </div>

                      <div className="mb-4">
                        <label htmlFor="contentDetails" className="block text-sm font-medium text-gray-700 mb-1">
                          รายละเอียดเนื้อหา
                        </label>
                        {formData.lessons[selectedLesson].content[selectedContent].typecontent === "text" ? (
                          <div className="border border-gray-300 rounded-md">
                            {/* Simple text editor toolbar */}
                            <div className="flex flex-wrap items-center gap-1 p-2 border-b border-gray-200 bg-gray-50">
                              <button className="p-1 hover:bg-gray-200 rounded">
                                <svg
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <path d="M4 7V4h16v3"></path>
                                  <path d="M9 20h6"></path>
                                  <path d="M12 4v16"></path>
                                </svg>
                              </button>
                              <button className="p-1 hover:bg-gray-200 rounded">
                                <svg
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path>
                                </svg>
                              </button>
                              <button className="p-1 hover:bg-gray-200 rounded">
                                <svg
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <line x1="8" y1="6" x2="21" y2="6"></line>
                                  <line x1="8" y1="12" x2="21" y2="12"></line>
                                  <line x1="8" y1="18" x2="21" y2="18"></line>
                                  <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                  <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                  <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                </svg>
                              </button>
                              <span className="h-4 w-px bg-gray-300 mx-1"></span>
                              <button className="p-1 hover:bg-gray-200 rounded font-bold">B</button>
                              <button className="p-1 hover:bg-gray-200 rounded italic">I</button>
                              <button className="p-1 hover:bg-gray-200 rounded underline">U</button>
                            </div>
                            <textarea
                              id="contentDetails"
                              name="details"
                              value={formData.lessons[selectedLesson].content[selectedContent].details}
                              onChange={(e) => handleContentInputChange(e, selectedLesson, selectedContent)}
                              rows={10}
                              className="w-full px-3 py-2 focus:outline-none bg-gray-50"
                              placeholder="กรอกเนื้อหาบทเรียน"
                            />
                          </div>
                        ) : (
                          <div>
                            <input
                              type="text"
                              id="contentDetails"
                              name="details"
                              value={formData.lessons[selectedLesson].content[selectedContent].details}
                              onChange={(e) => handleContentInputChange(e, selectedLesson, selectedContent)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50 mb-2"
                              placeholder="ใส่ลิงก์วิดีโอ (YouTube, Vimeo, ฯลฯ)"
                            />

                            <div
                              className="w-full h-24 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
                              onClick={() => document.getElementById("videoUpload")?.click()}
                              onDragOver={(e) => {
                                e.preventDefault()
                                e.currentTarget.classList.add("border-[#008268]", "bg-[#E6F2F0]")
                              }}
                              onDragLeave={(e) => {
                                e.preventDefault()
                                e.currentTarget.classList.remove("border-[#008268]", "bg-[#E6F2F0]")
                              }}
                              onDrop={(e) => {
                                e.preventDefault()
                                e.currentTarget.classList.remove("border-[#008268]", "bg-[#E6F2F0]")

                                const files = e.dataTransfer.files
                                if (files.length > 0 && files[0].type.startsWith("video/")) {
                                  // ในสถานการณ์จริงจะต้องมีการอัพโหลดไฟล์ไปยังเซิร์ฟเวอร์
                                  // และนำ URL ที่ได้มาใส่ในฟิลด์ details
                                  console.log("Uploading video:", files[0].name)

                                  // สำหรับตัวอย่าง เราจะแสดงชื่อไฟล์ในฟิลด์ details
                                  const updatedLessons = [...formData.lessons]
                                  updatedLessons[selectedLesson].content[selectedContent].details =
                                    `Uploaded: ${files[0].name}`

                                  setFormData((prev) => ({
                                    ...prev,
                                    lessons: updatedLessons,
                                  }))
                                } else {
                                  alert("กรุณาอัพโหลดไฟล์วิดีโอเท่านั้น")
                                }
                              }}
                            >
                              <input
                                type="file"
                                id="videoUpload"
                                accept="video/*"
                                className="hidden"
                                onChange={(e) => {
                                  const files = e.target.files
                                  if (files && files.length > 0) {
                                    // ในสถานการณ์จริงจะต้องมีการอัพโหลดไฟล์ไปยังเซิร์ฟเวอร์
                                    console.log("Uploading video:", files[0].name)

                                    // สำหรับตัวอย่าง เราจะแสดงชื่อไฟล์ในฟิลด์ details
                                    const updatedLessons = [...formData.lessons]
                                    updatedLessons[selectedLesson].content[selectedContent].details =
                                      `Uploaded: ${files[0].name}`

                                    setFormData((prev) => ({
                                      ...prev,
                                      lessons: updatedLessons,
                                    }))
                                  }
                                }}
                              />
                              <Upload className="h-6 w-6 text-gray-400 mb-1" />
                              <p className="text-xs text-gray-500">คลิกหรือลากไฟล์วิดีโอมาวางที่นี่</p>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="mb-4">
                        <label htmlFor="contentTime" className="block text-sm font-medium text-gray-700 mb-1">
                          ระยะเวลาเนื้อหา (วินาที)
                        </label>
                        <input
                          type="number"
                          id="contentTime"
                          name="time"
                          value={formData.lessons[selectedLesson].content[selectedContent].time}
                          onChange={(e) => handleContentInputChange(e, selectedLesson, selectedContent)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                          placeholder="กรอกระยะเวลาเนื้อหา (วินาที)"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          {Math.floor(formData.lessons[selectedLesson].content[selectedContent].time / 3600)} ชั่วโมง{" "}
                          {Math.floor((formData.lessons[selectedLesson].content[selectedContent].time % 3600) / 60)} นาที
                        </p>
                      </div>

                      <div className="flex justify-end">
                        <button
                          onClick={() => deleteContent(selectedLesson, selectedContent)}
                          className="px-3 py-1 text-sm text-red-600 border border-red-300 rounded-md hover:bg-red-50 flex items-center"
                          disabled={formData.lessons[selectedLesson].content.length <= 1}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          ลบเนื้อหานี้
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => confirmDeleteLesson(selectedLesson)}
                    className="flex items-center px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50"
                    disabled={formData.lessons.length <= 1}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    <span>ลบบทเรียน</span>
                  </button>
                  <button
                    onClick={() => saveLesson(selectedLesson)}
                    className="px-4 py-2 bg-[#008268] hover:bg-[#006e58] text-white rounded-md flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    บันทึกบทเรียน
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
