"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { Upload, ChevronDown, Plus, Trash2, Save, AlertTriangle, X, GripVertical, Check, Clock } from "lucide-react"
import { getCoursesData } from "@/data/allCourses"
import { getQuizById, saveQuiz } from "@/data/quizQuestion"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

type QuestionType = "text" | "image" | "text_and_image" | "video"
type ChoiceType = "text" | "image"

interface Choice {
  id: string
  content: string
  type: ChoiceType
  isCorrect: boolean
  imageUrl?: string
}

interface Question {
  id: string
  title: string
  type: QuestionType
  content: string
  imageUrl?: string
  videoTimestamp?: number // เพิ่มเวลาที่จะแสดงคำถามในวิดีโอ (วินาที)
  choices: Choice[] // เพิ่ม choices เพื่อแก้ไขข้อผิดพลาด
}

interface QuizFormData {
  id: string
  name: string
  course: string
  courseId: string
  lessonId: string // เพิ่ม lessonId
  contentId: string // เพิ่ม contentId
  description: string
  timeLimit: number
  passingScore: number
  status: string
  questions: Question[]
}

// SortableQuestion component for drag and drop
function SortableQuestion({
  question,
  index,
  isSelected,
  onSelect,
}: {
  question: Question
  index: number
  isSelected: boolean
  onSelect: () => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: question.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`p-3 rounded-md cursor-pointer mb-2 ${
        isSelected ? "bg-[#E6F2F0] border border-[#008268]" : "bg-white border border-gray-200"
      } ${isDragging ? "shadow-md" : ""}`}
    >
      <div className="flex items-center">
        <div
          {...attributes}
          {...listeners}
          className="mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
        >
          <GripVertical size={16} />
        </div>
        <span className="text-sm font-medium mr-2">{index + 1}.</span>
        <div className="flex-1 text-sm truncate" onClick={onSelect}>
          {question.title ? question.title : `คำถามที่ ${index + 1}`}
        </div>
        {question.videoTimestamp !== undefined && (
          <div className="flex items-center text-xs text-gray-500 ml-2">
            <Clock size={12} className="mr-1" />
            <span>{formatTime(question.videoTimestamp)}</span>
          </div>
        )}
      </div>
    </div>
  )
}

// ฟังก์ชันแปลงเวลาเป็นรูปแบบ mm:ss
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs < 10 ? "0" : ""}${secs}`
}

export default function QuizEditor({ quizId }: { quizId?: string }) {
  const [activeTab, setActiveTab] = useState<"quiz" | "questions">("quiz")
  const [selectedQuestion, setSelectedQuestion] = useState<number>(0)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const questionImageRef = useRef<HTMLInputElement>(null)
  const choiceImageRefs = useRef<(HTMLInputElement | null)[]>([])
  const questionDropzoneRef = useRef<HTMLDivElement>(null)
  const choiceDropzoneRefs = useRef<(HTMLDivElement | null)[]>([])

  // ดึงข้อมูลควิซจาก ID ทันที (ไม่ต้องรอ)
  const existingQuiz = quizId ? getQuizById(quizId) : null

  // ดึงข้อมูลคอร์สทั้งหมด
  const courses = getCoursesData

  // State สำหรับเก็บบทเรียนของคอร์สที่เลือก
  const [lessons, setLessons] = useState<any[]>([])
  // State สำหรับเก็บเนื้อหาของบทเรียนที่เลือก
  const [contents, setContents] = useState<any[]>([])

  // สร้างคำถามตัวอย่าง
  const createSampleQuestions = (): Question[] => {
    if (existingQuiz) {
      // สร้างคำถามตัวอย่างจากข้อมูลที่มีอยู่
      return existingQuiz.questions.map((q, i) => ({
        id: q.id || `question-${i + 1}`,
        title: q.title || `คำถามที่ ${i + 1}: ${existingQuiz.name}`,
        type: (q.type as QuestionType) || "text",
        content: q.content || `เนื้อหาคำถามเกี่ยวกับ${existingQuiz.name} ข้อที่ ${i + 1}`,
        imageUrl: q.imageUrl,
        videoTimestamp: q.videoTimestamp || (i + 1) * 30,
        choices: q.choices.map((c, j) => ({
          id: c.id || `q${i + 1}-choice-${j + 1}`,
          content: c.content || `ตัวเลือกที่ ${j + 1}`,
          type: (c.type as ChoiceType) || "text",
          isCorrect: c.isCorrect || false,
          imageUrl: c.imageUrl,
        })),
      }))
    } else {
      // สร้างคำถามเปล่าสำหรับควิซใหม่
      return [
        {
          id: "question-1",
          title: "",
          type: "text",
          content: "",
          choices: [
            { id: "q1-choice-1", content: "", type: "text", isCorrect: false },
            { id: "q1-choice-2", content: "", type: "text", isCorrect: false },
            { id: "q1-choice-3", content: "", type: "text", isCorrect: false },
            { id: "q1-choice-4", content: "", type: "text", isCorrect: false },
          ],
          videoTimestamp: 30, // เริ่มต้นที่ 30 วินาที
        },
      ]
    }
  }

  // สร้าง initial form data จากข้อมูลที่มีอยู่หรือค่าเริ่มต้น
  const initialFormData: QuizFormData = {
    id: existingQuiz?.id || `quiz-${Date.now()}`,
    name: existingQuiz?.name || "",
    course: existingQuiz?.course || "",
    courseId: existingQuiz?.courseId || "",
    lessonId: existingQuiz?.lessonId || "",
    contentId: existingQuiz?.contentId || "",
    description: existingQuiz?.description || "",
    timeLimit: existingQuiz?.timeLimit || 30, // เวลาในการทำควิซ (นาที)
    passingScore: 100, // กำหนดเป็น 100% เสมอ
    status: existingQuiz?.status || "draft",
    questions: createSampleQuestions(),
  }

  const [formData, setFormData] = useState<QuizFormData>(initialFormData)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [questionToDelete, setQuestionToDelete] = useState<number | null>(null)
  const [saveSuccess, setSaveSuccess] = useState<boolean | null>(null)
  const [showSaveConfirm, setShowSaveConfirm] = useState(false)
  const [isDraggingOver, setIsDraggingOver] = useState<number | null>(null)
  const [isQuestionDraggingOver, setIsQuestionDraggingOver] = useState(false)

  // เพิ่ม useEffect เพื่อจัดการ refs สำหรับรูปภาพตัวเลือก
  useEffect(() => {
    // ตรวจสอบว่ามีคำถามที่เลือกอยู่หรือไม่
    if (formData.questions.length > 0) {
      // กำหนดให้ choiceImageRefs มีขนาดเท่ากับจำนวนตัวเลือกในคำถามปัจจุบัน
      choiceImageRefs.current = Array(formData.questions[selectedQuestion].choices.length).fill(null)
      choiceDropzoneRefs.current = Array(formData.questions[selectedQuestion].choices.length).fill(null)
    }
  }, [selectedQuestion, formData.questions])

  // เพิ่ม useEffect เพื่อโหลดบทเรียนเมื่อเลือกคอร์ส
  useEffect(() => {
    if (formData.courseId) {
      const selectedCourse = courses.find((course) => course.id === formData.courseId)
      if (selectedCourse) {
        setLessons(selectedCourse.lesson)
        // รีเซ็ตบทเรียนและเนื้อหาที่เลือก
        if (!existingQuiz) {
          setFormData((prev) => ({
            ...prev,
            lessonId: "",
            contentId: "",
          }))
          setContents([])
        }
      }
    } else {
      setLessons([])
      setContents([])
    }
  }, [formData.courseId, courses, existingQuiz])

  // เพิ่ม useEffect เพื่อโหลดเนื้อหาเมื่อเลือกบทเรียน
  useEffect(() => {
    if (formData.lessonId) {
      const selectedLesson = lessons.find((lesson) => lesson.id === formData.lessonId)
      if (selectedLesson) {
        // กรองเฉพาะเนื้อหาที่เป็นวิดีโอ
        const videoContents = selectedLesson.content.filter((content: { typecontent: string }) => content.typecontent === "video")
        setContents(videoContents)
        // รีเซ็ตเนื้อหาที่เลือก
        if (!existingQuiz) {
          setFormData((prev) => ({
            ...prev,
            contentId: videoContents.length > 0 ? videoContents[0].id : "",
          }))
        }
      }
    } else {
      setContents([])
    }
  }, [formData.lessonId, lessons, existingQuiz])

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleQuestionInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    index: number,
  ) => {
    const { name, value } = e.target
    const updatedQuestions = [...formData.questions]
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      [name]: value,
    }
    setFormData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // เพิ่มฟังก์ชันสำหรับจัดการการเปลี่ยนแปลงเวลาในวิดีโอ
  const handleVideoTimestampChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = Number.parseInt(e.target.value, 10) || 0
    const updatedQuestions = [...formData.questions]
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      videoTimestamp: value,
    }
    setFormData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  const handleChoiceInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    questionIndex: number,
    choiceIndex: number,
  ) => {
    const { name, value } = e.target
    const updatedQuestions = [...formData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices]

    updatedChoices[choiceIndex] = {
      ...updatedChoices[choiceIndex],
      [name]: value,
    }

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setFormData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // เปลี่ยนจาก radio เป็น checkbox เพื่อให้เลือกได้หลายข้อ
  const handleCorrectAnswerChange = (questionIndex: number, choiceIndex: number) => {
    const updatedQuestions = [...formData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices]

    // Toggle isCorrect สำหรับตัวเลือกที่คลิก
    updatedChoices[choiceIndex] = {
      ...updatedChoices[choiceIndex],
      isCorrect: !updatedChoices[choiceIndex].isCorrect,
    }

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setFormData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  const handleChoiceTypeChange = (questionIndex: number, choiceIndex: number, type: ChoiceType) => {
    const updatedQuestions = [...formData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices]

    updatedChoices[choiceIndex] = {
      ...updatedChoices[choiceIndex],
      type,
    }

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setFormData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันสำหรับการ resize รูปภาพ
  const resizeImage = (file: File, maxWidth: number, maxHeight: number): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (event) => {
        const img = document.createElement("img")
        img.src = event.target?.result as string
        img.onload = () => {
          const canvas = document.createElement("canvas")
          let width = img.width
          let height = img.height

          // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width)
              width = maxWidth
            }
          } else {
            if (height > maxHeight) {
              width = Math.round((width * maxHeight) / height)
              height = maxHeight
            }
          }

          canvas.width = width
          canvas.height = height
          const ctx = canvas.getContext("2d")
          ctx?.drawImage(img, 0, 0, width, height)

          // แปลงเป็น base64
          const dataUrl = canvas.toDataURL("image/jpeg", 0.9)
          resolve(dataUrl)
        }
      }
    })
  }

  const handleQuestionImageUpload = async (e: React.ChangeEvent<HTMLInputElement>, questionIndex: number) => {
    const file = e.target.files?.[0]
    if (file) {
      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert("ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...formData.questions]
        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          imageUrl: resizedImage,
        }
        setFormData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        alert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  const handleChoiceImageUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    questionIndex: number,
    choiceIndex: number,
  ) => {
    const file = e.target.files?.[0]
    if (file) {
      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert("ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...formData.questions]
        const updatedChoices = [...updatedQuestions[questionIndex].choices]

        updatedChoices[choiceIndex] = {
          ...updatedChoices[choiceIndex],
          imageUrl: resizedImage,
        }

        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          choices: updatedChoices,
        }

        setFormData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        alert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  // ฟังก์ชันสำหรับการ drag and drop รูปภาพคำถาม
  const handleQuestionDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsQuestionDraggingOver(true)
  }

  const handleQuestionDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsQuestionDraggingOver(false)
  }

  const handleQuestionDrop = async (e: React.DragEvent<HTMLDivElement>, questionIndex: number) => {
    e.preventDefault()
    setIsQuestionDraggingOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]

      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!file.type.match("image.*")) {
        alert("กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น")
        return
      }

      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert("ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...formData.questions]
        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          imageUrl: resizedImage,
        }

        setFormData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        alert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  // ฟังก์ชันสำหรับการ drag and drop รูปภาพตัวเลือก
  const handleChoiceDragOver = (e: React.DragEvent<HTMLDivElement>, choiceIndex: number) => {
    e.preventDefault()
    setIsDraggingOver(choiceIndex)
  }

  const handleChoiceDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDraggingOver(null)
  }

  const handleChoiceDrop = async (e: React.DragEvent<HTMLDivElement>, questionIndex: number, choiceIndex: number) => {
    e.preventDefault()
    setIsDraggingOver(null)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]

      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!file.type.match("image.*")) {
        alert("กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น")
        return
      }

      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert("ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...formData.questions]
        const updatedChoices = [...updatedQuestions[questionIndex].choices]

        updatedChoices[choiceIndex] = {
          ...updatedChoices[choiceIndex],
          imageUrl: resizedImage,
        }

        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          choices: updatedChoices,
        }

        setFormData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        alert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  const triggerQuestionImageInput = () => {
    questionImageRef.current?.click()
  }

  const triggerChoiceImageInput = (index: number) => {
    choiceImageRefs.current[index]?.click()
  }

  const addNewQuestion = () => {
    const newQuestion: Question = {
      id: `question-${formData.questions.length + 1}`,
      title: "",
      type: "text",
      content: "",
      choices: [
        { id: `q${formData.questions.length + 1}-choice-1`, content: "", type: "text", isCorrect: false },
        { id: `q${formData.questions.length + 1}-choice-2`, content: "", type: "text", isCorrect: false },
        { id: `q${formData.questions.length + 1}-choice-3`, content: "", type: "text", isCorrect: false },
        { id: `q${formData.questions.length + 1}-choice-4`, content: "", type: "text", isCorrect: false },
      ],
      videoTimestamp: 30, // เริ่มต้นที่ 30 วินาที
    }
    setFormData((prev) => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }))
    setSelectedQuestion(formData.questions.length)
  }

  const confirmDeleteQuestion = (index: number) => {
    setQuestionToDelete(index)
    setShowDeleteConfirm(true)
  }

  const deleteQuestion = () => {
    if (questionToDelete === null || formData.questions.length <= 1) return

    const updatedQuestions = formData.questions.filter((_, i) => i !== questionToDelete)
    setFormData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))

    if (selectedQuestion >= updatedQuestions.length) {
      setSelectedQuestion(updatedQuestions.length - 1)
    } else if (selectedQuestion === questionToDelete) {
      setSelectedQuestion(Math.max(0, questionToDelete - 1))
    }

    setShowDeleteConfirm(false)
    setQuestionToDelete(null)
  }

  const saveQuestion = (index: number) => {
    console.log("Saving question:", formData.questions[index])
    // API call to save the question would go here

    // แสดงการบันทึกสำเร็จ
    setSaveSuccess(true)
    setTimeout(() => setSaveSuccess(null), 3000)
  }

  const confirmSaveAllChanges = () => {
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!formData.name.trim()) {
      alert("กรุณากรอกชื่อควิซ")
      setActiveTab("quiz")
      return
    }

    if (!formData.courseId) {
      alert("กรุณาเลือกคอร์สที่เกี่ยวข้อง")
      setActiveTab("quiz")
      return
    }

    if (!formData.lessonId) {
      alert("กรุณาเลือกบทเรียนที่เกี่ยวข้อง")
      setActiveTab("quiz")
      return
    }

    // ตรวจสอบ contentId
    if (!formData.contentId) {
      alert("กรุณาเลือกเนื้อหาวิดีโอที่เกี่ยวข้อง")
      setActiveTab("quiz")
      return
    }

    // ตรวจสอบคำถามทุกข้อ
    const invalidQuestions = formData.questions.findIndex((q, idx) => {
      if (!q.title.trim()) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        alert(`กรุณากรอกคำถามข้อที่ ${idx + 1}`)
        return true
      }

      // ตรวจสอบตัวเลือก
      const emptyChoices = q.choices.some((c) => c.type === "text" && !c.content.trim())
      if (emptyChoices) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        alert(`กรุณากรอกตัวเลือกให้ครบทุกข้อในคำถามข้อที่ ${idx + 1}`)
        return true
      }

      // ตรวจสอบว่ามีการเลือกคำตอบที่ถูกต้องอย่างน้อย 1 ข้อ
      const hasCorrectAnswer = q.choices.some((c) => c.isCorrect)
      if (!hasCorrectAnswer) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        alert(`กรุณาเลือกคำตอบที่ถูกต้องอย่างน้อย 1 ข้อในคำถามข้อที่ ${idx + 1}`)
        return true
      }

      // ตรวจสอบว่ามีการกำหนดเวลาในวิดีโอ
      if (q.videoTimestamp === undefined) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        alert(`กรุณากำหนดเวลาที่จะแสดงคำถามในวิดีโอสำหรับคำถามข้อที่ ${idx + 1}`)
        return true
      }

      return false
    })

    if (invalidQuestions !== -1) {
      return
    }

    setShowSaveConfirm(true)
  }

  const saveAllChanges = () => {
    console.log("Saving all changes:", formData)

    // เตรียมข้อมูลสำหรับบันทึก
    const quizData = {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      course: formData.course,
      courseId: formData.courseId,
      lessonId: formData.lessonId,
      contentId: formData.contentId,
      status: formData.status,
      timeLimit: formData.timeLimit,
      passingScore: formData.passingScore,
      questions: formData.questions,
      questionCount: formData.questions.length,
    }

    // บันทึกข้อมูลควิซ
    const result = saveQuiz(quizData)

    if (result) {
      // แสดงการบันทึกสำเร็จ
      setSaveSuccess(true)
      setTimeout(() => setSaveSuccess(null), 3000)
    } else {
      alert("เกิดข้อผิดพลาดในการบันทึกข้อมูล")
    }

    setShowSaveConfirm(false)
  }

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      setFormData((prev) => {
        // Find the indices of the dragged item and the drop target
        const oldIndex = prev.questions.findIndex((question) => question.id === active.id)
        const newIndex = prev.questions.findIndex((question) => question.id === over.id)

        // Update selectedQuestion to follow the dragged item
        if (selectedQuestion === oldIndex) {
          setSelectedQuestion(newIndex)
        } else if (selectedQuestion >= newIndex && selectedQuestion < oldIndex) {
          setSelectedQuestion(selectedQuestion + 1)
        } else if (selectedQuestion <= newIndex && selectedQuestion > oldIndex) {
          setSelectedQuestion(selectedQuestion - 1)
        }

        return {
          ...prev,
          questions: arrayMove(prev.questions, oldIndex, newIndex),
        }
      })
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with save button */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">{quizId ? "แก้ไขควิซ" : "สร้างควิซใหม่"}</h1>
        <div className="flex items-center gap-3">
          {saveSuccess && (
            <div className="bg-green-50 text-green-700 px-3 py-1 rounded-md flex items-center">
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              บันทึกสำเร็จ
            </div>
          )}
          <button
            onClick={confirmSaveAllChanges}
            className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md font-medium flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            บันทึก
          </button>
        </div>
      </div>

      {/* Navigation tabs */}
      <div className="flex border-b border-gray-200">
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${
            activeTab === "quiz" ? "text-[#008268] border-b-2 border-[#008268]" : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("quiz")}
        >
          ข้อมูลควิซ
        </button>
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${
            activeTab === "questions"
              ? "text-[#008268] border-b-2 border-[#008268]"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("questions")}
        >
          จัดการคำถาม
        </button>
      </div>

      {/* Quiz form */}
      {activeTab === "quiz" && (
        <div className="p-6">
          {/* Two columns for details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - Quiz details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดของควิซ</h3>

                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    ชื่อควิซ
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกชื่อควิซ"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="courseId" className="block text-sm font-medium text-gray-700 mb-1">
                    คอร์สที่เกี่ยวข้อง
                  </label>
                  <div className="relative">
                    <select
                      id="courseId"
                      name="courseId"
                      value={formData.courseId}
                      onChange={(e) => {
                        const selectedCourse = courses.find((course) => course.id === e.target.value)
                        setFormData((prev) => ({
                          ...prev,
                          courseId: e.target.value,
                          course: selectedCourse ? selectedCourse.name : "",
                        }))
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="">เลือกคอร์ส</option>
                      {courses.map((course) => (
                        <option key={course.id} value={course.id}>
                          {course.name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                {/* เพิ่มส่วนเลือกบทเรียน */}
                <div className="mb-4">
                  <label htmlFor="lessonId" className="block text-sm font-medium text-gray-700 mb-1">
                    บทเรียนที่เกี่ยวข้อง
                  </label>
                  <div className="relative">
                    <select
                      id="lessonId"
                      name="lessonId"
                      value={formData.lessonId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                      disabled={!formData.courseId}
                    >
                      <option value="">เลือกบทเรียน</option>
                      {lessons.map((lesson) => (
                        <option key={lesson.id} value={lesson.id}>
                          {lesson.name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                {/* เพิ่มส่วนเลือกเนื้อหาวิดีโอ */}
                <div className="mb-4">
                  <label htmlFor="contentId" className="block text-sm font-medium text-gray-700 mb-1">
                    เนื้อหาวิดีโอที่เกี่ยวข้อง
                  </label>
                  <div className="relative">
                    <select
                      id="contentId"
                      name="contentId"
                      value={formData.contentId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                      disabled={!formData.lessonId || contents.length === 0}
                    >
                      <option value="">เลือกเนื้อหาวิดีโอ</option>
                      {contents.map((content) => (
                        <option key={content.id} value={content.id}>
                          {content.name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  {contents.length === 0 && formData.lessonId && (
                    <p className="text-xs text-red-500 mt-1">ไม่พบเนื้อหาวิดีโอในบทเรียนนี้</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    คำอธิบายควิซ
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={5}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำอธิบายควิซ"
                  />
                </div>
              </div>
            </div>

            {/* Right column - Additional details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดเพิ่มเติม</h3>

                <div className="mb-4">
                  <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 mb-1">
                    เวลาในการทำควิซ (นาที)
                  </label>
                  <input
                    type="number"
                    id="timeLimit"
                    name="timeLimit"
                    min="1"
                    max="180"
                    value={formData.timeLimit}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                    สถานะควิซ
                  </label>
                  <div className="relative">
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="draft">แบบร่าง</option>
                      <option value="published">เผยแพร่</option>
                      <option value="disabled">ปิดใช้งาน</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">จำนวนคำถาม</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-700">
                    {formData.questions.length} คำถาม
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Questions form */}
      {activeTab === "questions" && (
        <div className="flex h-[calc(100vh-220px)] min-h-[500px]">
          {/* Left sidebar - Question list with drag and drop */}
          <div className="w-full md:w-1/3 lg:w-3/10 border-r border-gray-200 bg-gray-50 p-4 overflow-y-auto">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext
                items={formData.questions.map((question) => question.id)}
                strategy={verticalListSortingStrategy}
              >
                {formData.questions.map((question, index) => (
                  <SortableQuestion
                    key={question.id}
                    question={question}
                    index={index}
                    isSelected={selectedQuestion === index}
                    onSelect={() => setSelectedQuestion(index)}
                  />
                ))}
              </SortableContext>
            </DndContext>

            <button
              onClick={addNewQuestion}
              className="mt-4 w-full flex items-center justify-center p-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-1" />
              <span className="text-sm">เพิ่มคำถาม</span>
            </button>
          </div>

          {/* Right content - Question editor */}
          <div className="w-full md:w-2/3 lg:w-7/10 p-6 overflow-y-auto">
            {formData.questions.length > 0 && (
              <div>
                <div className="mb-4">
                  <label htmlFor="questionType" className="block text-sm font-medium text-gray-700 mb-1">
                    ประเภทคำถาม
                  </label>
                  <div className="relative">
                    <select
                      id="questionType"
                      name="type"
                      value={formData.questions[selectedQuestion].type}
                      onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="text">ข้อความ</option>
                      <option value="image">รูปภาพ</option>
                      <option value="text_and_image">ข้อความและรูปภาพ</option>
                      <option value="video">วิดีโอ</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                {/* เพิ่มส่วนกำหนดเวลาในวิดีโอ */}
                <div className="mb-4">
                  <label htmlFor="videoTimestamp" className="block text-sm font-medium text-gray-700 mb-1">
                    เวลาที่จะแสดงคำถามในวิดีโอ (วินาที)
                  </label>
                  <div className="flex items-center">
                    <input
                      type="number"
                      id="videoTimestamp"
                      name="videoTimestamp"
                      min="0"
                      value={formData.questions[selectedQuestion].videoTimestamp || 0}
                      onChange={(e) => handleVideoTimestampChange(e, selectedQuestion)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                      placeholder="กรอกเวลาที่จะแสดงคำถาม (วินาที)"
                    />
                    <div className="ml-2 text-sm text-gray-500">
                      {formData.questions[selectedQuestion].videoTimestamp !== undefined &&
                        formatTime(formData.questions[selectedQuestion].videoTimestamp)}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">คำถามจะปรากฏเมื่อวิดีโอเล่นถึงเวลาที่กำหนด</p>
                </div>

                <div className="mb-4">
                  <label htmlFor="questionTitle" className="block text-sm font-medium text-gray-700 mb-1">
                    คำถาม
                  </label>
                  <input
                    type="text"
                    id="questionTitle"
                    name="title"
                    value={formData.questions[selectedQuestion].title}
                    onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำถาม"
                  />
                </div>

                {(formData.questions[selectedQuestion].type === "image" ||
                  formData.questions[selectedQuestion].type === "text_and_image") && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">รูปภาพประกอบคำถาม</label>
                    <input
                      type="file"
                      ref={questionImageRef}
                      onChange={(e) => handleQuestionImageUpload(e, selectedQuestion)}
                      className="hidden"
                      accept="image/*"
                    />
                    <div
                      ref={questionDropzoneRef}
                      onClick={triggerQuestionImageInput}
                      onDragOver={handleQuestionDragOver}
                      onDragLeave={handleQuestionDragLeave}
                      onDrop={(e) => handleQuestionDrop(e, selectedQuestion)}
                      className={`relative w-full h-40 bg-gray-100 rounded-lg border-2 ${
                        isQuestionDraggingOver ? "border-[#008268] bg-[#E6F2F0]" : "border-dashed border-gray-300"
                      } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50`}
                    >
                      {formData.questions[selectedQuestion].imageUrl ? (
                        <div className="relative w-full h-full">
                          <Image
                            src={formData.questions[selectedQuestion].imageUrl || "/placeholder.svg"}
                            alt="Question image"
                            fill
                            sizes="100%"
                            className="object-contain rounded-lg"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                            <div className="bg-white p-2 rounded-full">
                              <Upload className="h-5 w-5 text-gray-700" />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <Upload className="h-12 w-12 text-gray-400 mb-2" />
                          <p className="text-sm text-gray-500">คลิกหรือลากไฟล์มาวางที่นี่เพื่ออัพโหลดรูปภาพ</p>
                          <p className="text-xs text-gray-400 mt-1">ขนาดแนะนำ 500x500 px</p>
                        </>
                      )}
                    </div>
                  </div>
                )}
                {(formData.questions[selectedQuestion].type === "text" ||
                  formData.questions[selectedQuestion].type === "text_and_image") && (
                  <div className="mb-4">
                    <label htmlFor="questionContent" className="block text-sm font-medium text-gray-700 mb-1">
                      รายละเอียดคำถาม
                    </label>
                    <textarea
                      id="questionContent"
                      name="content"
                      value={formData.questions[selectedQuestion].content}
                      onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                      placeholder="กรอกรายละเอียดคำถาม (ถ้ามี)"
                    />
                  </div>
                )}
                <div className="mt-6 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">ตัวเลือกคำตอบ</h3>

                  <div className="space-y-4">
                    {formData.questions[selectedQuestion].choices.map((choice, choiceIndex) => (
                      <div key={choice.id} className="border border-gray-200 rounded-md p-4">
                        <div className="flex items-center mb-3">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={`choice-${choiceIndex}`}
                              name={`correct-answer-${selectedQuestion}-${choiceIndex}`}
                              checked={choice.isCorrect}
                              onChange={() => handleCorrectAnswerChange(selectedQuestion, choiceIndex)}
                              className="h-4 w-4 text-[#008268] focus:ring-[#008268] border-gray-300 rounded"
                            />
                            <label htmlFor={`choice-${choiceIndex}`} className="ml-2 text-sm font-medium text-gray-700">
                              คำตอบที่ถูกต้อง
                            </label>
                          </div>

                          <div className="ml-auto">
                            <div className="relative">
                              <select
                                value={choice.type}
                                onChange={(e) =>
                                  handleChoiceTypeChange(selectedQuestion, choiceIndex, e.target.value as ChoiceType)
                                }
                                className="text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-8 pl-2 py-1"
                              >
                                <option value="text">ข้อความ</option>
                                <option value="image">รูปภาพ</option>
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <ChevronDown className="h-3 w-3 text-gray-500" />
                              </div>
                            </div>
                          </div>
                        </div>

                        {choice.type === "text" ? (
                          <input
                            type="text"
                            name="content"
                            value={choice.content}
                            onChange={(e) => handleChoiceInputChange(e, selectedQuestion, choiceIndex)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                            placeholder={`ตัวเลือกที่ ${choiceIndex + 1}`}
                          />
                        ) : (
                          <div>
                            <input
                              type="file"
                              ref={(el) => {
                                // แก้ไขวิธีการกำหนดค่า ref
                                if (choiceImageRefs.current.length > choiceIndex) {
                                  choiceImageRefs.current[choiceIndex] = el
                                }
                              }}
                              onChange={(e) => handleChoiceImageUpload(e, selectedQuestion, choiceIndex)}
                              className="hidden"
                              accept="image/*"
                            />
                            <div
                              ref={(el) => {
                                if (choiceDropzoneRefs.current.length > choiceIndex) {
                                  choiceDropzoneRefs.current[choiceIndex] = el
                                }
                              }}
                              onClick={() => triggerChoiceImageInput(choiceIndex)}
                              onDragOver={(e) => handleChoiceDragOver(e, choiceIndex)}
                              onDragLeave={handleChoiceDragLeave}
                              onDrop={(e) => handleChoiceDrop(e, selectedQuestion, choiceIndex)}
                              className={`relative w-full h-24 bg-gray-100 rounded-lg border-2 ${
                                isDraggingOver === choiceIndex
                                  ? "border-[#008268] bg-[#E6F2F0]"
                                  : "border-dashed border-gray-300"
                              } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50`}
                            >
                              {choice.imageUrl ? (
                                <div className="relative w-full h-full">
                                  <Image
                                    src={choice.imageUrl || "/placeholder.svg"}
                                    alt={`Choice ${choiceIndex + 1}`}
                                    fill
                                    sizes="100%"
                                    className="object-contain rounded-lg"
                                  />
                                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                                    <div className="bg-white p-2 rounded-full">
                                      <Upload className="h-4 w-4 text-gray-700" />
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <>
                                  <Upload className="h-8 w-8 text-gray-400 mb-1" />
                                  <p className="text-xs text-gray-500">คลิกหรือลากไฟล์มาวางที่นี่</p>
                                  <p className="text-xs text-gray-400">ขนาดแนะนำ 500x500 px</p>
                                </>
                              )}
                            </div>
                            <input
                              type="text"
                              name="content"
                              value={choice.content}
                              onChange={(e) => handleChoiceInputChange(e, selectedQuestion, choiceIndex)}
                              className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                              placeholder="คำอธิบายรูปภาพ (ถ้ามี)"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => confirmDeleteQuestion(selectedQuestion)}
                    className="flex items-center px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50"
                    disabled={formData.questions.length <= 1}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    <span>ลบคำถาม</span>
                  </button>
                  <button
                    onClick={() => saveQuestion(selectedQuestion)}
                    className="px-4 py-2 bg-[#008268] hover:bg-[#006e58] text-white rounded-md flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    บันทึกคำถาม
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete confirmation modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>

            <div className="flex items-center mb-4">
              <div className="bg-red-100 p-3 rounded-full mr-4">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">ยืนยันการลบคำถาม</h3>
            </div>

            <p className="text-gray-600 mb-6">
              คุณแน่ใจหรือไม่ว่าต้องการลบคำถาม "
              {questionToDelete !== null && formData.questions[questionToDelete]?.title
                ? formData.questions[questionToDelete].title
                : `คำถามที่ ${questionToDelete !== null ? questionToDelete + 1 : ""}`}
              "? การกระทำนี้ไม่สามารถย้อนกลับได้
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                ยกเลิก
              </button>
              <button
                onClick={deleteQuestion}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                ยืนยันการลบ
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Save confirmation modal */}
      {showSaveConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
            <button
              onClick={() => setShowSaveConfirm(false)}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>

            <div className="flex items-center mb-4">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <AlertTriangle className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">ยืนยันการบันทึกควิซ</h3>
            </div>

            <p className="text-gray-600 mb-6">
              คุณแน่ใจหรือไม่ว่าต้องการบันทึกควิซ "{formData.name}"? ควิซนี้มีคำถามทั้งหมด {formData.questions.length} ข้อ
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSaveConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                ยกเลิก
              </button>
              <button
                onClick={saveAllChanges}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center"
              >
                <Check className="h-4 w-4 mr-1" />
                ยืนยันการบันทึก
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
