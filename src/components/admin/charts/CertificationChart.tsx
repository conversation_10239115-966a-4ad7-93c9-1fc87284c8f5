"use client"

import { useState } from "react"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
} from "chart.js"
import { Line, Doughnut } from "react-chartjs-2"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
)

export default function CertificationChart() {
  const [chartType, setChartType] = useState<"trend" | "breakdown">("trend")

  // Trend data showing enrollments, completions, and certifications over time
  const trendData = {
    labels: [
      "ม.ค.", "ก.พ.", "มี.ค.", "เม.ย.", "พ.ค.", "มิ.ย.",
      "ก.ค.", "ส.ค.", "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค."
    ],
    datasets: [
      {
        label: "การลงทะเบียน",
        data: [120, 135, 142, 158, 167, 189, 201, 234, 245, 267, 289, 312],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        fill: true,
        tension: 0.4,
      },
      {
        label: "การเสร็จสิ้นคอร์ส",
        data: [89, 98, 105, 118, 125, 142, 156, 178, 189, 201, 223, 245],
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.1)",
        fill: true,
        tension: 0.4,
      },
      {
        label: "การออกใบรับรอง",
        data: [67, 74, 82, 89, 95, 108, 123, 134, 145, 156, 167, 189],
        borderColor: "rgb(245, 158, 11)",
        backgroundColor: "rgba(245, 158, 11, 0.1)",
        fill: true,
        tension: 0.4,
      },
    ],
  }

  // Certification success rate breakdown
  const breakdownData = {
    labels: ["ได้รับใบรับรอง", "เสร็จสิ้นแต่ไม่ได้ใบรับรอง", "ยังไม่เสร็จสิ้น"],
    datasets: [
      {
        data: [189, 56, 67],
        backgroundColor: [
          "rgba(16, 185, 129, 0.8)",
          "rgba(245, 158, 11, 0.8)",
          "rgba(239, 68, 68, 0.8)",
        ],
        borderColor: [
          "rgb(16, 185, 129)",
          "rgb(245, 158, 11)",
          "rgb(239, 68, 68)",
        ],
        borderWidth: 2,
      },
    ],
  }

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            family: "Inter, sans-serif",
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
    },
    interaction: {
      intersect: false,
      mode: "index" as const,
    },
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "bottom" as const,
        labels: {
          font: {
            family: "Inter, sans-serif",
          },
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
            const percentage = ((context.parsed / total) * 100).toFixed(1)
            return `${context.label}: ${context.parsed} คน (${percentage}%)`
          }
        }
      },
    },
  }

  // Conversion rates calculation
  const latestMonth = trendData.datasets[0].data.length - 1
  const enrollments = trendData.datasets[0].data[latestMonth]
  const completions = trendData.datasets[1].data[latestMonth]
  const certifications = trendData.datasets[2].data[latestMonth]
  
  const completionRate = ((completions / enrollments) * 100).toFixed(1)
  const certificationRate = ((certifications / completions) * 100).toFixed(1)
  const overallCertificationRate = ((certifications / enrollments) * 100).toFixed(1)

  return (
    <div className="w-full">
      {/* Chart Type Selector */}
      <div className="flex justify-end mb-4">
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setChartType("trend")}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              chartType === "trend"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            แนวโน้ม
          </button>
          <button
            onClick={() => setChartType("breakdown")}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              chartType === "breakdown"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            สัดส่วน
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-64 mb-4">
        {chartType === "trend" ? (
          <Line data={trendData} options={lineOptions} />
        ) : (
          <Doughnut data={breakdownData} options={doughnutOptions} />
        )}``
      </div>

      {/* Conversion Metrics */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-600">อัตราการเสร็จสิ้น</p>
          <p className="text-lg font-semibold text-blue-800">{completionRate}%</p>
          <p className="text-xs text-blue-600">จากการลงทะเบียน</p>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <p className="text-sm text-green-600">อัตราการได้ใบรับรอง</p>
          <p className="text-lg font-semibold text-green-800">{certificationRate}%</p>
          <p className="text-xs text-green-600">จากการเสร็จสิ้น</p>
        </div>
        <div className="text-center p-3 bg-yellow-50 rounded-lg">
          <p className="text-sm text-yellow-600">อัตราโดยรวม</p>
          <p className="text-lg font-semibold text-yellow-800">{overallCertificationRate}%</p>
          <p className="text-xs text-yellow-600">จากการลงทะเบียน</p>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
        <h4 className="font-medium text-green-800 mb-2">ข้อมูลเชิงลึก</h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• อัตราการออกใบรับรองเพิ่มขึ้น 15.2% ในเดือนนี้</li>
          <li>• 77.1% ของผู้ที่เสร็จสิ้นคอร์สได้รับใบรับรอง</li>
          <li>• แนวโน้มการเติบโตของใบรับรองสูงกว่าการลงทะเบียน</li>
        </ul>
      </div>
    </div>
  )
}
