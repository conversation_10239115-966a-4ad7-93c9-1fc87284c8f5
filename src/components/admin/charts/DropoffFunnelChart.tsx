"use client"

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import { Bar } from "react-chartjs-2"

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

export default function DropoffFunnelChart() {
  const funnelData = [
    { stage: "ลงทะเบียน", count: 1245, percentage: 100 },
    { stage: "เริ่มเรียน", count: 1089, percentage: 87.5 },
    { stage: "ความคืบหน้า 50%", count: 756, percentage: 60.7 },
    { stage: "เสร็จสิ้นคอร์ส", count: 623, percentage: 50.0 },
  ]

  const data = {
    labels: funnelData.map(item => item.stage),
    datasets: [
      {
        label: "จำนวนนักเรียน",
        data: funnelData.map(item => item.count),
        backgroundColor: [
          "rgba(59, 130, 246, 0.8)",
          "rgba(16, 185, 129, 0.8)",
          "rgba(245, 158, 11, 0.8)",
          "rgba(239, 68, 68, 0.8)",
        ],
        borderColor: [
          "rgb(59, 130, 246)",
          "rgb(16, 185, 129)",
          "rgb(245, 158, 11)",
          "rgb(239, 68, 68)",
        ],
        borderWidth: 2,
        borderRadius: 4,
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex
            const percentage = funnelData[dataIndex].percentage
            return `${context.parsed.y} คน (${percentage}%)`
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
            size: 12,
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
    },
  }

  return (
    <div className="w-full">
      {/* Chart */}
      <div className="h-64 mb-4">
        <Bar data={data} options={options} />
      </div>

      {/* Conversion Rates */}
      <div className="space-y-3">
        {funnelData.map((item, index) => {
          const dropoffRate = index > 0 ? 
            ((funnelData[index - 1].count - item.count) / funnelData[index - 1].count * 100).toFixed(1) : 
            null
          
          return (
            <div key={item.stage} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div 
                  className="w-4 h-4 rounded-full mr-3"
                  style={{ backgroundColor: data.datasets[0].backgroundColor[index] }}
                />
                <span className="font-medium text-gray-800">{item.stage}</span>
              </div>
              <div className="text-right">
                <div className="font-semibold text-gray-800">{item.count.toLocaleString()} คน</div>
                <div className="text-sm text-gray-500">
                  {item.percentage}%
                  {dropoffRate && (
                    <span className="text-red-500 ml-2">(-{dropoffRate}%)</span>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Key Insights */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">ข้อมูลเชิงลึก</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• อัตราการเริ่มเรียนหลังลงทะเบียน: 87.5%</li>
          <li>• อัตราการเสร็จสิ้นคอร์ส: 50.0%</li>
          <li>• จุดที่มีการออกมากที่สุด: ระหว่างเริ่มเรียนถึง 50%</li>
        </ul>
      </div>
    </div>
  )
}
