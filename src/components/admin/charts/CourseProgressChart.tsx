"use client"

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import { Bar } from "react-chartjs-2"

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

export default function CourseProgressChart() {
  const courseData = [
    { name: "Pathology Fundamentals", progress: 78, enrolled: 245 },
    { name: "Pneumonia Treatment", progress: 85, enrolled: 189 },
    { name: "Clinical Skills", progress: 62, enrolled: 156 },
    { name: "Emergency Medicine", progress: 71, enrolled: 134 },
    { name: "Cardiology Basics", progress: 89, enrolled: 98 },
    { name: "Pediatric Care", progress: 56, enrolled: 87 },
    { name: "Surgical Procedures", progress: 67, enrolled: 76 },
    { name: "Radiology Reading", progress: 74, enrolled: 65 },
  ]

  const data = {
    labels: courseData.map(course => course.name),
    datasets: [
      {
        label: "ความคืบหน้าเฉลี่ย (%)",
        data: courseData.map(course => course.progress),
        backgroundColor: courseData.map(course => {
          if (course.progress >= 80) return "rgba(16, 185, 129, 0.8)"
          if (course.progress >= 70) return "rgba(245, 158, 11, 0.8)"
          return "rgba(239, 68, 68, 0.8)"
        }),
        borderColor: courseData.map(course => {
          if (course.progress >= 80) return "rgb(16, 185, 129)"
          if (course.progress >= 70) return "rgb(245, 158, 11)"
          return "rgb(239, 68, 68)"
        }),
        borderWidth: 2,
        borderRadius: 4,
      },
    ],
  }

  const options = {
    indexAxis: "y" as const,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex
            const enrolled = courseData[dataIndex].enrolled
            return [
              `ความคืบหน้าเฉลี่ย: ${context.parsed.x}%`,
              `นักเรียนที่ลงทะเบียน: ${enrolled} คน`
            ]
          }
        }
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
          callback: function(value: any) {
            return value + "%"
          }
        },
      },
      y: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
            size: 11,
          },
          callback: function(value: any, index: number) {
            const label = courseData[index]?.name || ""
            return label.length > 20 ? label.substring(0, 20) + "..." : label
          }
        },
      },
    },
  }

  return (
    <div className="w-full">
      {/* Chart */}
      <div className="h-80 mb-4">
        <Bar data={data} options={options} />
      </div>

      {/* Course Details */}
      <div className="space-y-2 max-h-40 overflow-y-auto">
        {courseData.map((course, index) => (
          <div key={course.name} className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: data.datasets[0].backgroundColor[index] }}
              />
              <span className="text-sm font-medium text-gray-800 truncate" title={course.name}>
                {course.name}
              </span>
            </div>
            <div className="text-right">
              <div className="text-sm font-semibold text-gray-800">{course.progress}%</div>
              <div className="text-xs text-gray-500">{course.enrolled} คน</div>
            </div>
          </div>
        ))}
      </div>

      {/* Performance Summary */}
      <div className="mt-4 grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <p className="text-sm text-gray-500">คอร์สที่ดีเยี่ยม</p>
          <p className="text-lg font-semibold text-green-600">
            {courseData.filter(c => c.progress >= 80).length}
          </p>
          <p className="text-xs text-gray-500">≥80%</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">คอร์สที่ดี</p>
          <p className="text-lg font-semibold text-yellow-600">
            {courseData.filter(c => c.progress >= 70 && c.progress < 80).length}
          </p>
          <p className="text-xs text-gray-500">70-79%</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">ต้องปรับปรุง</p>
          <p className="text-lg font-semibold text-red-600">
            {courseData.filter(c => c.progress < 70).length}
          </p>
          <p className="text-xs text-gray-500">&lt;70%</p>
        </div>
      </div>
    </div>
  )
}
