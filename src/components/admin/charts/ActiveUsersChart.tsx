"use client"

import { useState } from "react"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js"
import { Line } from "react-chartjs-2"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function ActiveUsersChart() {
  const [timeRange, setTimeRange] = useState<"30days" | "12months">("30days")

  // Mock data for 30 days
  const dailyData = {
    labels: Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return date.toLocaleDateString("th-TH", { month: "short", day: "numeric" })
    }),
    datasets: [
      {
        label: "ผู้ใช้ที่ใช้งานรายวัน",
        data: [
          120, 135, 142, 128, 156, 189, 167, 145, 178, 192, 
          156, 134, 167, 189, 201, 178, 156, 189, 234, 198,
          167, 145, 189, 212, 234, 189, 167, 178, 201, 156
        ],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        fill: true,
        tension: 0.4,
      },
    ],
  }

  // Mock data for 12 months
  const monthlyData = {
    labels: [
      "ม.ค.", "ก.พ.", "มี.ค.", "เม.ย.", "พ.ค.", "มิ.ย.",
      "ก.ค.", "ส.ค.", "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค."
    ],
    datasets: [
      {
        label: "ผู้ใช้ที่ใช้งานรายเดือน",
        data: [3200, 3450, 3890, 4120, 4350, 4680, 4920, 5150, 5380, 5620, 5890, 6120],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        fill: true,
        tension: 0.4,
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
    },
    interaction: {
      intersect: false,
      mode: "index" as const,
    },
  }

  return (
    <div className="w-full">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-4">
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setTimeRange("30days")}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              timeRange === "30days"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            30 วัน
          </button>
          <button
            onClick={() => setTimeRange("12months")}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              timeRange === "12months"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            12 เดือน
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-64">
        <Line 
          data={timeRange === "30days" ? dailyData : monthlyData} 
          options={options} 
        />
      </div>

      {/* Metrics Summary */}
      <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <p className="text-sm text-gray-500">เวลาเซสชันเฉลี่ย</p>
          <p className="text-lg font-semibold text-gray-800">45 นาที</p>
          <p className="text-xs text-green-600">+8.2% จากเดือนที่แล้ว</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">อัตราการกลับมาใช้</p>
          <p className="text-lg font-semibold text-gray-800">68%</p>
          <p className="text-xs text-green-600">+5.1% จากเดือนที่แล้ว</p>
        </div>
      </div>
    </div>
  )
}
