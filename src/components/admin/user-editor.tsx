"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { Upload, ChevronDown, Save, AlertTriangle, X, Check, Copy, RefreshCw } from "lucide-react"
import { getMedicalUsersData } from "@/data/allUsers"

interface UserFormData {
  id: string
  firstname: string
  lastname: string
  email: string
  position: string
  role: "student" | "lecturer" | "admin"
  password: string
  confirmPassword: string
  status: "active" | "inactive"
  profileImage: string | null
  lastLogin?: Date
}

export default function UserEditor({ userId }: { userId?: string }) {
  // ดึงข้อมูลผู้ใช้จาก ID ทันที (ไม่ต้องรอ)
  const existingUser = userId ? getMedicalUsersData.find((user) => user.id === userId) : null

  // สร้าง initial form data จากข้อมูลที่มีอยู่หรือค่าเริ่มต้น
  const initialFormData: UserFormData = {
    id: existingUser?.id || `user-${Date.now()}`,
    firstname: existingUser?.firstname || "",
    lastname: existingUser?.lastname || "",
    email: existingUser?.email || "",
    position: existingUser?.position || "",
    role: (existingUser?.role as "student" | "lecturer" | "admin") || "student",
    password: "",
    confirmPassword: "",
    status: (existingUser?.status as "active" | "inactive") || "active",
    profileImage: null,
    lastLogin: existingUser?.lastLogin,
  }

  const [formData, setFormData] = useState<UserFormData>(initialFormData)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState<boolean | null>(null)
  const [showSaveConfirm, setShowSaveConfirm] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isImageDraggingOver, setIsImageDraggingOver] = useState(false)
  // เพิ่ม state สำหรับการจัดการ autocomplete ตำแหน่ง
  // เพิ่มหลังจาก const [isImageDraggingOver, setIsImageDraggingOver] = useState(false)
  const [positionSuggestions, setPositionSuggestions] = useState<string[]>([])
  const [showPositionSuggestions, setShowPositionSuggestions] = useState(false)
  const [positionQuery, setPositionQuery] = useState("")
  // เพิ่ม state สำหรับจัดการ dropdown
  // เพิ่มหลังจาก state อื่นๆ
  const [showRoleDropdown, setShowRoleDropdown] = useState(false)
  const [showStatusDropdown, setShowStatusDropdown] = useState(false)
  const roleRef = useRef<HTMLDivElement>(null)
  const statusRef = useRef<HTMLDivElement>(null)

  // เพิ่ม state สำหรับเก็บรหัสผ่านที่สร้างใหม่และสถานะการคัดลอก
  // เพิ่มหลังจาก state อื่นๆ
  const [generatedPassword, setGeneratedPassword] = useState<string>("")
  const [showGeneratedPassword, setShowGeneratedPassword] = useState<boolean>(false)
  const [copySuccess, setCopySuccess] = useState<boolean>(false)

  // เพิ่ม useEffect สำหรับจัดการการคลิกนอก dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (roleRef.current && !roleRef.current.contains(event.target as Node)) {
        setShowRoleDropdown(false)
      }
      if (statusRef.current && !statusRef.current.contains(event.target as Node)) {
        setShowStatusDropdown(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    // เพิ่ม CSS สำหรับทำให้ dropdown แสดงขึ้นด้านบน
    const style = document.createElement("style")
    style.textContent = `
      select {
        /* ลบ default arrow ของ select */
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }
      
      /* ทำให้ dropdown แสดงขึ้นด้านบน */
      select:focus {
        position: relative;
      }
      
      select:focus option {
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // ล้าง error เมื่อมีการแก้ไขข้อมูล
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }))
    }

    // จัดการกับการพิมพ์ในช่องตำแหน่ง
    if (name === "position") {
      setPositionQuery(value)
      searchPositions(value)
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  // ฟังก์ชันสำหรับการ resize รูปภาพ
  const resizeImage = (file: File, maxWidth: number, maxHeight: number): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (event) => {
        const img = document.createElement("img")
        img.src = event.target?.result as string
        img.onload = () => {
          const canvas = document.createElement("canvas")
          let width = img.width
          let height = img.height

          // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width)
              width = maxWidth
            }
          } else {
            if (height > maxHeight) {
              width = Math.round((width * maxHeight) / height)
              height = maxHeight
            }
          }

          canvas.width = width
          canvas.height = height
          const ctx = canvas.getContext("2d")
          ctx?.drawImage(img, 0, 0, width, height)

          // แปลงเป็น base64
          const dataUrl = canvas.toDataURL("image/jpeg", 0.9)
          resolve(dataUrl)
        }
      }
    })
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert("ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 300x300
        const resizedImage = await resizeImage(file, 300, 300)

        setFormData((prev) => ({
          ...prev,
          profileImage: resizedImage,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        alert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  // ฟังก์ชันสำหรับการ drag and drop รูปภาพ
  const handleImageDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsImageDraggingOver(true)
  }

  const handleImageDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsImageDraggingOver(false)
  }

  const handleImageDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsImageDraggingOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]

      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!file.type.match("image.*")) {
        alert("กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น")
        return
      }

      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert("ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 300x300
        const resizedImage = await resizeImage(file, 300, 300)

        setFormData((prev) => ({
          ...prev,
          profileImage: resizedImage,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        alert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // ตรวจสอบชื่อ
    if (!formData.firstname.trim()) {
      newErrors.firstname = "กรุณากรอกชื่อ"
    }

    // ตรวจสอบนามสกุล
    if (!formData.lastname.trim()) {
      newErrors.lastname = "กรุณากรอกนามสกุล"
    }

    // ตรวจสอบอีเมล
    if (!formData.email.trim()) {
      newErrors.email = "กรุณากรอกอีเมล"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "รูปแบบอีเมลไม่ถูกต้อง"
    }

    // ตรวจสอบตำแหน่ง
    if (!formData.position.trim()) {
      newErrors.position = "กรุณากรอกตำแหน่ง"
    }

    // ตรวจสอบรหัสผ่านเฉพาะกรณีเพิ่มผู้ใช้ใหม่
    if (!userId) {
      if (!formData.password) {
        newErrors.password = "กรุณากรอกรหัสผ่าน"
      } else if (formData.password.length < 8) {
        newErrors.password = "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "รหัสผ่านไม่ตรงกัน"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const confirmSaveChanges = () => {
    if (validateForm()) {
      setShowSaveConfirm(true)
    }
  }

  const saveChanges = () => {
    console.log("Saving user:", formData)
    // API call to save the user would go here

    // แสดงการบันทึกสำเร็จ
    setSaveSuccess(true)
    setTimeout(() => setSaveSuccess(null), 3000)
    setShowSaveConfirm(false)
  }

  // เพิ่มฟังก์ชันสำหรับสร้างรหัสผ่านแบบสุ่ม
  // เพิ่มหลังจากฟังก์ชัน saveChanges
  const generateRandomPassword = () => {
    // กำหนดความยาวและอักขระที่ใช้ในการสร้างรหัสผ่าน
    const length = 12
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    let newPassword = ""

    // สร้างรหัสผ่านแบบสุ่ม
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length)
      newPassword += charset[randomIndex]
    }

    setGeneratedPassword(newPassword)
    setShowGeneratedPassword(true)
  }

  // เพิ่มฟังก์ชันสำหรับคัดลอกรหัสผ่านไปยัง clipboard
  // เพิ่มหลังจากฟังก์ชัน generateRandomPassword
  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(generatedPassword)
      .then(() => {
        setCopySuccess(true)
        // แสดงข้อความสำเร็จเป็นเวลา 2 วินาที
        setTimeout(() => setCopySuccess(false), 2000)
      })
      .catch((err) => {
        console.error("Failed to copy: ", err)
      })
  }

  // เพิ่มฟังก์ชันสำหรับดึงตำแหน่งที่มีอยู่แล้วในระบบ
  // เพิ่มหลังจากฟังก์ชัน saveChanges
  const getExistingPositions = (): string[] => {
    // ดึงตำแหน่งที่มีอยู่แล้วจากข้อมูลผู้ใช้ทั้งหมด
    const positions = getMedicalUsersData.map((user) => user.position)
    // กรองเอาเฉพาะตำแหน่งที่ไม่ซ้ำกัน
    return [...new Set(positions)]
  }

  // เพิ่มฟังก์ชันสำหรับค้นหาตำแหน่งที่ตรงกับคำค้นหา
  // เพิ่มหลังจากฟังก์ชัน getExistingPositions
  const searchPositions = (query: string) => {
    if (!query.trim()) {
      setPositionSuggestions([])
      setShowPositionSuggestions(false)
      return
    }

    const existingPositions = getExistingPositions()
    const filteredPositions = existingPositions.filter((position) =>
      position.toLowerCase().includes(query.toLowerCase()),
    )

    setPositionSuggestions(filteredPositions)
    setShowPositionSuggestions(true)
  }

  // เพิ่มฟังก์ชันสำหรับเลือกตำแหน่งจากรายการ
  // เพิ่มหลังจากฟังก์ชัน searchPositions
  const selectPosition = (position: string) => {
    setFormData((prev) => ({
      ...prev,
      position,
    }))
    setShowPositionSuggestions(false)
  }

  // แก้ไขฟังก์ชัน handleInputChange เพื่อรองรับการเลือกจาก custom dropdown
  const handleRoleChange = (role: "student" | "lecturer" | "admin") => {
    setFormData((prev) => ({
      ...prev,
      role,
    }))
    setShowRoleDropdown(false)
  }

  const handleStatusChange = (status: "active" | "inactive") => {
    setFormData((prev) => ({
      ...prev,
      status,
    }))
    setShowStatusDropdown(false)
  }

  return (
    <div className="bg-white rounded-lg lg:h-[75vh] shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with save button */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">{userId ? "แก้ไขข้อมูลผู้ใช้" : "เพิ่มผู้ใช้ใหม่"}</h1>
        <div className="flex items-center gap-3">
          {saveSuccess && (
            <div className="bg-green-50 text-green-700 px-3 py-1 rounded-md flex items-center">
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              บันทึกสำเร็จ
            </div>
          )}
          <button
            onClick={confirmSaveChanges}
            className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md font-medium flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            บันทึก
          </button>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Profile Image */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-4">รูปโปรไฟล์</h3>
            <div className="mb-6 flex flex-col items-center">
              <input type="file" ref={fileInputRef} onChange={handleImageUpload} className="hidden" accept="image/*" />
              <div
                onClick={triggerFileInput}
                onDragOver={handleImageDragOver}
                onDragLeave={handleImageDragLeave}
                onDrop={handleImageDrop}
                className={`relative w-64 h-64 bg-gray-100 rounded-full border-2 ${
                  isImageDraggingOver ? "border-[#008268] bg-[#E6F2F0]" : "border-dashed border-gray-300"
                } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 overflow-hidden`}
              >
                {formData.profileImage ? (
                  <>
                    <Image
                      src={formData.profileImage || "/placeholder.svg"}
                      alt="Profile"
                      fill
                      sizes="100%"
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                      <div className="bg-white p-2 rounded-full">
                        <Upload className="h-5 w-5 text-gray-700" />
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <svg
                      className="w-24 h-24 text-gray-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <p className="text-sm text-gray-500 text-center px-4">คลิกหรือลากไฟล์มาวาง</p>
                    <p className="text-xs text-gray-400 mt-1 text-center">ขนาดแนะนำ 300x300 px</p>
                  </>
                )}
              </div>
            </div>

            {userId && (
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-2">ข้อมูลเพิ่มเติม</h4>
                <div className="text-sm text-gray-600 mb-2">
                  <span className="font-medium">รหัสผู้ใช้:</span> {formData.id}
                </div>
                {formData.lastLogin && (
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">เข้าสู่ระบบล่าสุด:</span>{" "}
                    {new Intl.DateTimeFormat("th-TH", {
                      day: "numeric",
                      month: "short",
                      year: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    }).format(formData.lastLogin)}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Middle column - Personal Information */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-4">ข้อมูลส่วนตัว</h3>

            <div className="mb-4">
              <label htmlFor="firstname" className="block text-sm font-medium text-gray-700 mb-1">
                ชื่อ <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="firstname"
                name="firstname"
                value={formData.firstname}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border ${
                  errors.firstname ? "border-red-300" : "border-gray-300"
                } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50`}
                placeholder="กรอกชื่อ"
              />
              {errors.firstname && <p className="text-red-500 text-xs mt-1">{errors.firstname}</p>}
            </div>

            <div className="mb-4">
              <label htmlFor="lastname" className="block text-sm font-medium text-gray-700 mb-1">
                นามสกุล <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="lastname"
                name="lastname"
                value={formData.lastname}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border ${
                  errors.lastname ? "border-red-300" : "border-gray-300"
                } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50`}
                placeholder="กรอกนามสกุล"
              />
              {errors.lastname && <p className="text-red-500 text-xs mt-1">{errors.lastname}</p>}
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                อีเมล <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border ${
                  errors.email ? "border-red-300" : "border-gray-300"
                } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50`}
                placeholder="กรอกอีเมล"
              />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div className="mb-4">
              <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                ตำแหน่ง <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  onFocus={() => {
                    if (formData.position) {
                      searchPositions(formData.position)
                    }
                  }}
                  onBlur={() => {
                    // ใช้ setTimeout เพื่อให้สามารถคลิกเลือกตำแหน่งได้ก่อนที่ dropdown จะหายไป
                    setTimeout(() => setShowPositionSuggestions(false), 200)
                  }}
                  className={`w-full px-3 py-2 border ${
                    errors.position ? "border-red-300" : "border-gray-300"
                  } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50`}
                  placeholder="กรอกตำแหน่ง"
                />
                {showPositionSuggestions && positionSuggestions.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {positionSuggestions.map((position, index) => (
                      <div
                        key={index}
                        className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                        onMouseDown={() => selectPosition(position)}
                      >
                        {position}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {errors.position && <p className="text-red-500 text-xs mt-1">{errors.position}</p>}
            </div>
          </div>

          {/* Right column - Account Information */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-4">ข้อมูลบัญชี</h3>

            <div className="mb-4">
              <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                บทบาท <span className="text-red-500">*</span>
              </label>
              <div className="relative" ref={roleRef}>
                <div
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => setShowRoleDropdown(!showRoleDropdown)}
                >
                  <span>
                    {formData.role === "student" ? "นักศึกษา" : formData.role === "lecturer" ? "อาจารย์" : "ผู้ดูแลระบบ"}
                  </span>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
                {showRoleDropdown && (
                  <div className="absolute bottom-full left-0 right-0 mb-1 bg-white border border-gray-300 rounded-md shadow-lg z-10">
                    <div
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleRoleChange("student")}
                    >
                      นักศึกษา
                    </div>
                    <div
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleRoleChange("lecturer")}
                    >
                      อาจารย์
                    </div>
                    <div
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleRoleChange("admin")}
                    >
                      ผู้ดูแลระบบ
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                สถานะ <span className="text-red-500">*</span>
              </label>
              <div className="relative" ref={statusRef}>
                <div
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => setShowStatusDropdown(!showStatusDropdown)}
                >
                  <span>{formData.status === "active" ? "ใช้งาน" : "ไม่ใช้งาน"}</span>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
                {showStatusDropdown && (
                  <div className="absolute bottom-full left-0 right-0 mb-1 bg-white border border-gray-300 rounded-md shadow-lg z-10">
                    <div
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleStatusChange("active")}
                    >
                      ใช้งาน
                    </div>
                    <div
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleStatusChange("inactive")}
                    >
                      ไม่ใช้งาน
                    </div>
                  </div>
                )}
              </div>
            </div>

            {userId ? (
              <>
                <div className="mb-4">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    รหัสผ่าน
                  </label>
                  <div className="relative">
                    <input
                      type="password"
                      id="password"
                      value="••••••••"
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <span className="text-xs text-gray-500">ข้อมูลถูกปกปิด</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">รหัสผ่านถูกปกปิดเพื่อความปลอดภัย</p>
                </div>

                <div className="flex mt-5 flex-col space-y-2 mb-4">
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={generateRandomPassword}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 flex items-center"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      สร้างรหัสผ่านใหม่
                    </button>

                    <button
                      type="button"
                      onClick={() => alert("ระบบจะส่งลิงก์รีเซ็ตรหัสผ่านไปยังอีเมลของผู้ใช้")}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      ส่งลิงก์รีเซ็ตรหัสผ่าน
                    </button>
                  </div>

                  {showGeneratedPassword && (
                    <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-md">
                      <p className="text-sm font-medium text-gray-700 mb-2">รหัสผ่านใหม่:</p>
                      <div className="flex items-center">
                        <div className="flex-1 bg-white px-3 py-2 border border-gray-300 rounded-md text-sm font-mono">
                          {generatedPassword}
                        </div>
                        <button
                          type="button"
                          onClick={copyToClipboard}
                          className="ml-2 p-2 bg-gray-100 hover:bg-gray-200 rounded-md"
                          title="คัดลอกรหัสผ่าน"
                        >
                          {copySuccess ? (
                            <Check className="h-5 w-5 text-green-600" />
                          ) : (
                            <Copy className="h-5 w-5 text-gray-600" />
                          )}
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        กรุณาคัดลอกรหัสผ่านนี้และส่งให้ผู้ใช้ รหัสผ่านจะถูกบันทึกเมื่อคุณกดปุ่ม "บันทึก"
                      </p>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <label htmlFor="initialPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    ตั้งรหัสผ่านเริ่มต้น <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="initialPassword"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border ${
                        errors.password ? "border-red-300" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50`}
                      placeholder="กรอกรหัสผ่านเริ่มต้น"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <svg
                          className="h-5 w-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="h-5 w-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                          />
                        </svg>
                      )}
                    </button>
                  </div>
                  {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                </div>
              </>
            )}

            {!userId && (
              <div className="mb-4">
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  ยืนยันรหัสผ่าน <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.confirmPassword ? "border-red-300" : "border-gray-300"
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50`}
                    placeholder="ยืนยันรหัสผ่าน"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    )}
                  </button>
                </div>
                {errors.confirmPassword && <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Save confirmation modal */}
      {showSaveConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
            <button
              onClick={() => setShowSaveConfirm(false)}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>

            <div className="flex items-center mb-4">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <AlertTriangle className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">ยืนยันการบันทึกข้อมูลผู้ใช้</h3>
            </div>

            <p className="text-gray-600 mb-6">
              คุณแน่ใจหรือไม่ว่าต้องการบันทึกข้อมูลผู้ใช้ "{formData.firstname} {formData.lastname}"?
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSaveConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                ยกเลิก
              </button>
              <button
                onClick={saveChanges}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center"
              >
                <Check className="h-4 w-4 mr-1" />
                ยืนยันการบันทึก
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

