"use client"

import React from "react"
import { courseService } from "@/services/courseService"

import { useState } from "react"
import { Search, ChevronLeft, ChevronRight, Edit, Trash2, MoreHorizontal, Eye } from "lucide-react"
import { FaPlus } from "react-icons/fa"
import { ChevronUp, ChevronDown } from "lucide-react"
import Link from "next/link"

export default function CoursesTable() {
  const [courses, setCourses] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [selectAll, setSelectAll] = useState(false)

  const itemsPerPage = 10

  // Fetch courses from API using courseService
  React.useEffect(() => {
    courseService.getCourses()
      .then(setCourses)
      .catch(err => console.error("Failed to fetch courses", err))
  }, [])

  // ฟังก์ชันค้นหา
  const filteredCourses = courses.filter(
    (course) =>
      (course.course_name || "").toLowerCase().includes(searchQuery.toLowerCase()) ||
      String(course.id).toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // ฟังก์ชันเรียงลำดับ
  const sortedCourses = [...filteredCourses].sort((a, b) => {
    if (!sortField) return 0
    const fieldA = a[sortField]
    const fieldB = b[sortField]
    if (typeof fieldA === "string" && typeof fieldB === "string") {
      return sortDirection === "asc" ? fieldA.localeCompare(fieldB) : fieldB.localeCompare(fieldA)
    }
    if (typeof fieldA === "number" && typeof fieldB === "number") {
      return sortDirection === "asc" ? fieldA - fieldB : fieldB - fieldA
    }
    return 0
  })

  // ฟังก์ชันแบ่งหน้า
  const totalPages = Math.ceil(sortedCourses.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCourses = sortedCourses.slice(startIndex, startIndex + itemsPerPage)

  // ฟังก์ชันเลือกแถว
  const toggleRowSelection = (id: string) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // ฟังก์ชันเลือกทั้งหมด
  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([])
    } else {
      setSelectedRows(paginatedCourses.map((course) => course.id))
    }
    setSelectAll(!selectAll)
  }

  // ฟังก์ชันเรียงลำดับ
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // ฟังก์ชันเปิด/ปิด dropdown
  const toggleDropdown = (slug: string) => {
    if (activeDropdown === slug) {
      setActiveDropdown(null)
    } else {
      setActiveDropdown(slug)
    }
  }

  // ฟังก์ชันลบคอร์ส
  const handleDeleteCourse = async (course: any) => {
    if (!window.confirm(`คุณต้องการลบคอร์ส "${course.course_name}" หรือไม่?`)) return;
    try {
      await courseService.deleteCourse(course.slug)
      setCourses((prev) => prev.filter((c) => c.id !== course.id))
      setActiveDropdown(null)
    } catch (err: any) {
      alert("เกิดข้อผิดพลาดในการลบคอร์ส: " + err.message)
    }
  }

  // แก้ไขฟังก์ชัน renderSortIcon ให้แสดงไอคอนตลอดเวลา
  function renderSortIcon(field: string): React.ReactNode {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4 text-gray-700" />
      ) : (
        <ChevronDown className="ml-1 h-4 w-4 text-gray-700" />
      )
    }
    // แสดงไอคอนทั้งขึ้นและลงเมื่อยังไม่ได้เรียงลำดับ
    return (
      <span className="ml-1 inline-flex flex-col">
        <ChevronUp className="h-3 w-3 -mb-1 text-gray-400" />
        <ChevronDown className="h-3 w-3 text-gray-400" />
      </span>
    )
  }
  return (
    <div className="w-full">
      {/* ส่วนค้นหาและปุ่มเพิ่มคอร์ส */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหาคอร์ส..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>

        <Link
          href="/admin/courses/new"
          className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md w-full font-bold sm:w-auto flex items-center justify-center gap-2"
        >
          เพิ่มคอร์สใหม่
          <FaPlus className="text-white text-sm" />
        </Link>
      </div>

      {/* ตาราง */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 rounded border-gray-300 bg-gray-50 text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center">
                  ชื่อคอร์ส
                  {renderSortIcon("name")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("lessons")}
              >
                <div className="flex items-center justify-center">
                  จำนวนบทเรียน
                  {renderSortIcon("lessons")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("students")}
              >
                <div className="flex items-center justify-center">
                  จำนวนผู้เรียน
                  {renderSortIcon("students")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("creator")}
              >
                <div className="flex items-center">
                  ผู้สร้าง
                  {renderSortIcon("creator")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("status")}
              >
                <div className="flex items-center">
                  สถานะ
                  {renderSortIcon("status")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                จัดการ
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedCourses.map((course) => (
              <tr key={course.id} className="hover:bg-gray-50">
                <td className="px-3 py-4 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    checked={selectedRows.includes(String(course.id))}
                    onChange={() => toggleRowSelection(String(course.id))}
                    className="h-4 w-4 rounded border-gray-300 bg-white text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{course.course_name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <div className="text-sm text-gray-900">{course.lesson_amount ?? '-'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <div className="text-sm text-gray-900">{course.student_count ?? '-'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{course.lecturer || '-'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${course.course_status
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-700"
                      }`}
                  >
                    {course.course_status ? "เผยแพร่" : "ไม่เผยแพร่"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                  <button onClick={() => toggleDropdown(course.slug)} className="text-gray-500 hover:text-gray-700">
                    <MoreHorizontal className="h-5 w-5" />
                  </button>

                  {activeDropdown === course.slug && (
                    <div className="right-6 mt-2 w-48 fixed rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                      <div className="py-1" role="menu" aria-orientation="vertical">
                        <button
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => {
                            console.log("View details:", course.slug)
                            setActiveDropdown(null)
                          }}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          ดูรายละเอียด
                        </button>
                        <Link
                          href={`/admin/courses/edit/${course.slug}`}
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setActiveDropdown(null)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          แก้ไข
                        </Link>
                        <button
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                          onClick={() => handleDeleteCourse(course)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          ลบ
                        </button>
                      </div>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
        <div className="text-sm text-gray-500">
          เลือก {selectedRows.length} จาก {filteredCourses.length} รายการ
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            หน้า {currentPage} จาก {totalPages}
          </span>
          <div className="flex gap-1">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

