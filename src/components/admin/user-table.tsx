"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import { Search, ChevronLeft, ChevronRight, Edit, Trash2, MoreHorizontal, Eye, UserPlus } from "lucide-react"
import { ChevronUp, ChevronDown } from "lucide-react"
import { getMedicalUsersData } from "@/data/allUsers"

export default function UsersTable() {
    const [searchQuery, setSearchQuery] = useState("")
    const [currentPage, setCurrentPage] = useState(1)
    const [selectedRows, setSelectedRows] = useState<string[]>([])
    const [sortField, setSortField] = useState<string | null>(null)
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
    const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
    const [selectAll, setSelectAll] = useState(false)

    const itemsPerPage = 10

    // ฟังก์ชันค้นหา
    const filteredUsers = getMedicalUsersData.filter(
        (user) =>
            user.firstname.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.lastname.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
            `${user.firstname} ${user.lastname}`.toLowerCase().includes(searchQuery.toLowerCase()),
    )

    // ฟังก์ชันเรียงลำดับ
    const sortedUsers = [...filteredUsers].sort((a, b) => {
        if (!sortField) return 0

        // Special case for fullname sorting
        if (sortField === "fullname") {
            const fullnameA = `${a.firstname} ${a.lastname}`
            const fullnameB = `${b.firstname} ${b.lastname}`
            return sortDirection === "asc" ? fullnameA.localeCompare(fullnameB) : fullnameB.localeCompare(fullnameA)
        }

        const fieldA = a[sortField as keyof typeof a]
        const fieldB = b[sortField as keyof typeof b]

        if (typeof fieldA === "string" && typeof fieldB === "string") {
            return sortDirection === "asc" ? fieldA.localeCompare(fieldB) : fieldB.localeCompare(fieldA)
        }

        if (typeof fieldA === "number" && typeof fieldB === "number") {
            return sortDirection === "asc" ? fieldA - fieldB : fieldB - fieldA
        }

        // For dates
        if (fieldA instanceof Date && fieldB instanceof Date) {
            return sortDirection === "asc" ? fieldA.getTime() - fieldB.getTime() : fieldB.getTime() - fieldA.getTime()
        }

        return 0
    })

    // ฟังก์ชันแบ่งหน้า
    const totalPages = Math.ceil(sortedUsers.length / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const paginatedUsers = sortedUsers.slice(startIndex, startIndex + itemsPerPage)

    // ฟังก์ชันเลือกแถว
    const toggleRowSelection = (id: string) => {
        if (selectedRows.includes(id)) {
            setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
        } else {
            setSelectedRows([...selectedRows, id])
        }
    }

    // ฟังก์ชันเลือกทั้งหมด
    const toggleSelectAll = () => {
        if (selectAll) {
            setSelectedRows([])
        } else {
            setSelectedRows(paginatedUsers.map((user) => user.id))
        }
        setSelectAll(!selectAll)
    }

    // ฟังก์ชันเรียงลำดับ
    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc")
        } else {
            setSortField(field)
            setSortDirection("asc")
        }
    }

    // ฟังก์ชันเปิด/ปิด dropdown
    const toggleDropdown = (id: string) => {
        if (activeDropdown === id) {
            setActiveDropdown(null)
        } else {
            setActiveDropdown(id)
        }
    }

    // แสดงไอคอนการเรียงลำดับ
    function renderSortIcon(field: string): React.ReactNode {
        if (sortField === field) {
            return sortDirection === "asc" ? (
                <ChevronUp className="ml-1 h-4 w-4 text-gray-400" />
            ) : (
                <ChevronDown className="ml-1 h-4 w-4 text-gray-400" />
            )
        }
        // แสดงไอคอนทั้งขึ้นและลงเมื่อยังไม่ได้เรียงลำดับ
        return (
            <span className="ml-1 inline-flex flex-col">
                <ChevronUp className="h-3 w-3 -mb-1 text-gray-400" />
                <ChevronDown className="h-3 w-3 text-gray-400" />
            </span>
        )
    }

    // ฟังก์ชันแปลงวันที่เป็นรูปแบบที่อ่านง่าย
    const formatDate = (date: Date): string => {
        return new Intl.DateTimeFormat("th-TH", {
            day: "numeric",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        }).format(date)
    }

    return (
        <div className="w-full">
            {/* ส่วนค้นหาและปุ่มเพิ่มผู้ใช้ */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                <div className="relative w-full sm:w-auto">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                        type="text"
                        placeholder="ค้นหาผู้ใช้..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
                    />
                </div>

                <button className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md w-full font-bold sm:w-auto flex items-center justify-center gap-2">
                    เพิ่มผู้ใช้ใหม่
                    <UserPlus className="h-4 w-4" />
                </button>
            </div>

            {/* ตาราง */}
            <div className="overflow-x-auto border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th scope="col" className="px-3 py-3 text-center">
                                <input
                                    type="checkbox"
                                    checked={selectAll}
                                    onChange={toggleSelectAll}
                                    className="h-4 w-4 rounded border-gray-300 bg-gray-50 text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                                />
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort("fullname")}
                            >
                                <div className="flex items-center">
                                    ชื่อ-นามสกุล
                                    {renderSortIcon("fullname")}
                                </div>
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort("position")}
                            >
                                <div className="flex items-center">
                                    ตำแหน่ง
                                    {renderSortIcon("position")}
                                </div>
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort("lastLogin")}
                            >
                                <div className="flex items-center">
                                    บทบาท
                                    {renderSortIcon("role")}
                                </div>
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort("status")}
                            >
                                <div className="flex items-center">
                                    สถานะ
                                    {renderSortIcon("status")}
                                </div>
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                                จัดการ
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {paginatedUsers.map((user) => (
                            <tr key={user.id} className="hover:bg-gray-50">
                                <td className="px-3 py-4 whitespace-nowrap text-center">
                                    <input
                                        type="checkbox"
                                        checked={selectedRows.includes(user.id)}
                                        onChange={() => toggleRowSelection(user.id)}
                                        className="h-4 w-4 rounded border-gray-300 bg-white text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                                    />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center space-x-3">

                                        <div className="h-10 w-10 relative">
                                            {user.profileImage ? (
                                                <img
                                                    src={user.profileImage || "/placeholder.svg"}
                                                    alt={`${user.firstname} ${user.lastname}`}
                                                    width={40}
                                                    height={40}
                                                    className="rounded-full object-cover"
                                                />
                                            ) : (
                                                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                    <span className="text-gray-500 font-medium text-sm">
                                                        {user.firstname.charAt(0)}
                                                        {user.lastname.charAt(0)}
                                                    </span>
                                                </div>
                                            )}
                                        </div>

                                        <div>
                                            <div className="text-sm font-medium text-gray-900">
                                                {user.firstname} {user.lastname}
                                            </div>
                                            <div className="text-sm text-gray-500">{user.email}</div>
                                        </div>
                                    </div>
                                </td>

                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{user.position}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{user.role}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <span
                                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${user.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-700"}`}
                                    >
                                        {user.status === "active" ? "ใช้งาน" : "ไม่ใช้งาน"}
                                    </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                                    <button onClick={() => toggleDropdown(user.id)} className="text-gray-500 hover:text-gray-700">
                                        <MoreHorizontal className="h-5 w-5" />
                                    </button>

                                    {activeDropdown === user.id && (
                                        <div className="fixed right-10 ipad-pro-landscape:mt-0 ipad-pro:mt-0 lg:-mt-24 md:mt-0 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                            <div className="py-1" role="menu" aria-orientation="vertical">
                                                <button
                                                    className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    onClick={() => {
                                                        console.log("Edit:", user.id)
                                                        setActiveDropdown(null)
                                                    }}
                                                >
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    แก้ไข
                                                </button>
                                                <button
                                                    className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                                                    onClick={() => {
                                                        console.log("Delete:", user.id)
                                                        setActiveDropdown(null)
                                                    }}
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    ลบ
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
            <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
                <div className="text-sm text-gray-500">
                    เลือก {selectedRows.length} จาก {filteredUsers.length} รายการ
                </div>

                <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-700">
                        หน้า {currentPage} จาก {totalPages}
                    </span>
                    <div className="flex gap-1">
                        <button
                            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                            className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <ChevronLeft className="h-4 w-4" />
                        </button>
                        <button
                            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                            className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <ChevronRight className="h-4 w-4" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
}

