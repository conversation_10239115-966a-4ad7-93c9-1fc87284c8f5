"use client"

import type React from "react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Search, ChevronLeft, ChevronRight, Edit, Trash2, MoreHorizontal, Eye } from "lucide-react"
import { FaPlus } from "react-icons/fa"
import { ChevronUp, ChevronDown } from "lucide-react"
import Link from "next/link"
import type { LearningPath } from "@/types/learning-paths"

interface LearningPathsTableProps {
  learningPaths: LearningPath[]
}

export default function LearningPathsTable({ learningPaths }: LearningPathsTableProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [selectAll, setSelectAll] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const itemsPerPage = 10

  // ฟังก์ชันค้นหา
  const filteredPaths = learningPaths.filter(
    (path) =>
      path.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      path.id.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // ฟังก์ชันเรียงลำดับ
  const sortedPaths = [...filteredPaths].sort((a, b) => {
    if (!sortField) return 0

    const fieldA = a[sortField as keyof typeof a]
    const fieldB = b[sortField as keyof typeof b]

    if (typeof fieldA === "string" && typeof fieldB === "string") {
      return sortDirection === "asc" ? fieldA.localeCompare(fieldB) : fieldB.localeCompare(fieldA)
    }

    if (typeof fieldA === "number" && typeof fieldB === "number") {
      return sortDirection === "asc" ? fieldA - fieldB : fieldB - fieldA
    }

    return 0
  })

  // ฟังก์ชันแบ่งหน้า
  const totalPages = Math.ceil(sortedPaths.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedPaths = sortedPaths.slice(startIndex, startIndex + itemsPerPage)

  // ฟังก์ชันเลือกแถว
  const toggleRowSelection = (id: string) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // ฟังก์ชันเลือกทั้งหมด
  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([])
    } else {
      setSelectedRows(paginatedPaths.map((path) => path.id))
    }
    setSelectAll(!selectAll)
  }

  // ฟังก์ชันเรียงลำดับ
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // ฟังก์ชัน toggle dropdown
  const toggleDropdown = (id: string) => {
    setActiveDropdown(activeDropdown === id ? null : id)
  }

  // ฟังก์ชันนำทางไปยังหน้าแก้ไข
  const handleEdit = (id: string) => {
    router.push(`/admin/learning-paths/edit/${id}`)
  }

  // ฟังก์ชันนำทางไปยังหน้าดูรายละเอียด
  const handleView = (id: string) => {
    router.push(`/admin/learning-paths/${id}`)
  }

  // แก้ไขฟังก์ชัน renderSortIcon ให้แสดงไอคอนตลอดเวลา
  function renderSortIcon(field: string): React.ReactNode {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4 text-gray-700" />
      ) : (
        <ChevronDown className="ml-1 h-4 w-4 text-gray-700" />
      )
    }
    // แสดงไอคอนทั้งขึ้นและลงเมื่อยังไม่ได้เรียงลำดับ
    return (
      <span className="ml-1 inline-flex flex-col">
        <ChevronUp className="h-3 w-3 -mb-1 text-gray-400" />
        <ChevronDown className="h-3 w-3 text-gray-400" />
      </span>
    )
  }

  return (
    <div className="w-full">
      {/* ส่วนค้นหาและปุ่มเพิ่มเส้นทาง */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหาเส้นทางการเรียนรู้..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>

        <Link
          href="/admin/learning-paths/new"
          className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md w-full font-bold sm:w-auto flex items-center justify-center gap-2"
        >
          เพิ่มเส้นทางใหม่
          <FaPlus className="text-white text-sm" />
        </Link>
      </div>

      {/* ตาราง */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 rounded border-gray-300 bg-gray-50 text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center">
                  ชื่อเส้นทางการเรียนรู้
                  {renderSortIcon("name")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("courseCount")}
              >
                <div className="flex items-center justify-center">
                  จำนวนคอร์ส
                  {renderSortIcon("courseCount")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("status")}
              >
                <div className="flex items-center">
                  สถานะ
                  {renderSortIcon("status")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                จัดการ
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedPaths.map((path) => (
              <tr key={path.id} className="hover:bg-gray-50">
                <td className="px-3 py-4 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    checked={selectedRows.includes(path.id)}
                    onChange={() => toggleRowSelection(path.id)}
                    className="h-4 w-4 rounded border-gray-300 bg-white text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{path.name}</div>
                  <div className="text-sm text-gray-500 mt-1 line-clamp-2">{path.description}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <div className="text-sm text-gray-900">{path.courseCount}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${
                      path.status === "published"
                        ? "bg-green-100 text-green-800"
                        : path.status === "draft"
                          ? "bg-gray-100 text-gray-800"
                          : "bg-red-100 text-red-700"
                    }`}
                  >
                    {path.status === "published" ? "เผยแพร่" : path.status === "draft" ? "แบบร่าง" : "เก็บถาวร"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                  <button onClick={() => toggleDropdown(path.id)} className="text-gray-500 hover:text-gray-700">
                    <MoreHorizontal className="h-5 w-5" />
                  </button>

                  {activeDropdown === path.id && (
                    <div className="right-6 mt-2 w-48 fixed rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                      <div className="py-1" role="menu" aria-orientation="vertical">
                        <button
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => {
                            console.log("View details:", path.id)
                            setActiveDropdown(null)
                          }}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          ดูรายละเอียด
                        </button>
                        <Link
                          href={`/admin/learning-paths/edit/${path.id}`}
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setActiveDropdown(null)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          แก้ไข
                        </Link>
                        <button
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                          onClick={() => {
                            console.log("Delete:", path.id)
                            setActiveDropdown(null)
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          ลบ
                        </button>
                      </div>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
        <div className="text-sm text-gray-500">
          เลือก {selectedRows.length} จาก {filteredPaths.length} รายการ
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            หน้า {currentPage} จาก {totalPages}
          </span>
          <div className="flex gap-1">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
