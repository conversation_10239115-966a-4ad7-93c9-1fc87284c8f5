"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core"
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Plus, GripVertical, X, ChevronDown } from "lucide-react"
import Link from "next/link"
import type { LearningPath } from "@/types/learning-paths"
import { getCoursesData } from "@/data/allCourses"
import ConfirmationModal from "@/components/admin/confirmation-popup"

interface LearningPathEditorProps {
  initialData?: LearningPath
  mode: "create" | "edit"
}

interface SelectedCourse {
  id: string
  name: string
  description: string
}

interface ModalState {
  isOpen: boolean
  type: "save" | "delete" | "success" | "error"
  title: string
  message: string
  onConfirm?: () => void
}

// Sortable Course Item Component
function SortableCourseItem({
  course,
  onRemove,
}: {
  course: SelectedCourse
  onRemove: (id: string) => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: course.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    console.log("Remove button clicked for course:", course.id, course.name)
    onRemove(course.id)
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center gap-4 ${
        isDragging ? "opacity-50" : ""
      }`}
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab hover:cursor-grabbing text-gray-400 hover:text-gray-600"
      >
        <GripVertical className="h-5 w-5" />
      </div>

      <div className="flex-1">
        <h4 className="font-medium text-black">{course.name}</h4>
        <p className="text-sm text-gray-500 mt-1 line-clamp-2">{course.description}</p>
      </div>

      <button
        onClick={handleRemoveClick}
        className="text-red-500 hover:text-red-700 p-1 hover:bg-red-50 rounded transition-colors"
        type="button"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

export default function LearningPathEditor({ initialData, mode }: LearningPathEditorProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    description: initialData?.description || "",
    status: initialData?.status || ("draft" as const),
  })

  const [selectedCourses, setSelectedCourses] = useState<SelectedCourse[]>([])
  const [showCourseDropdown, setShowCourseDropdown] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [modal, setModal] = useState<ModalState>({
    isOpen: false,
    type: "save",
    title: "",
    message: "",
  })

  // Initialize selected courses from initial data
  useEffect(() => {
    console.log("=== Learning Path Editor Debug ===")
    console.log("Initial data:", initialData)
    console.log("Available courses:", getCoursesData)

    if (initialData?.courseIds && initialData.courseIds.length > 0) {
      console.log("Course IDs to load:", initialData.courseIds)

      const courses = initialData.courseIds
        .map((courseId) => {
          const course = getCoursesData.find((c) => c.id === courseId)
          console.log(`Looking for course ${courseId}:`, course)
          return course
            ? {
                id: course.id,
                name: course.name || "ไม่มีชื่อคอร์ส",
                description: course.description || "",
              }
            : null
        })
        .filter(Boolean) as SelectedCourse[]

      console.log("Successfully loaded courses:", courses)
      setSelectedCourses(courses)
    } else {
      console.log("No course IDs found in initial data")
      setSelectedCourses([])
    }
  }, [initialData])

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // Handle drag end
  function handleDragEnd(event: any) {
    const { active, over } = event

    if (active.id !== over.id) {
      setSelectedCourses((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  // Get available courses (not already selected)
  const availableCourses = getCoursesData.filter(
    (course) =>
      !selectedCourses.some((selected) => selected.id === course.id) &&
      (course.name || "").toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Add course to path
  const addCourse = (courseId: string) => {
    const course = getCoursesData.find((c) => c.id === courseId)
    if (course) {
      setSelectedCourses((prev) => [
        ...prev,
        {
          id: course.id,
          name: course.name || "ไม่มีชื่อคอร์ส",
          description: course.description || "",
        },
      ])
      setShowCourseDropdown(false)
      setSearchTerm("")
    }
  }

  // Remove course from path with confirmation
  const removeCourse = (courseId: string) => {
    console.log("removeCourse called with courseId:", courseId)
    const course = selectedCourses.find((c) => c.id === courseId)
    console.log("Found course:", course)

    if (course) {
      console.log("Setting delete confirmation modal")
      setModal({
        isOpen: true,
        type: "delete",
        title: "ลบคอร์สออกจากเส้นทาง",
        message: `คุณต้องการลบคอร์ส "${course.name}" ออกจากเส้นทางการเรียนรู้นี้หรือไม่?`,
        onConfirm: () => {
          console.log("Confirming delete for course:", course.name)
          // ลบคอร์สจริง
          setSelectedCourses((prev) => prev.filter((c) => c.id !== courseId))
          // ปิด modal confirmation
          setModal((prev) => ({ ...prev, isOpen: false }))
          // แสดง success modal
          setTimeout(() => {
            setModal({
              isOpen: true,
              type: "success",
              title: "ลบคอร์สสำเร็จ",
              message: `ลบคอร์ส "${course.name}" ออกจากเส้นทางการเรียนรู้เรียบร้อยแล้ว`,
            })
          }, 100)
        },
      })
    } else {
      console.log("Course not found for ID:", courseId)
    }
  }

  // Handle save confirmation
  const handleSaveClick = () => {
    if (!formData.name.trim()) {
      setModal({
        isOpen: true,
        type: "error",
        title: "ข้อมูลไม่ครบถ้วน",
        message: "กรุณากรอกชื่อเส้นทางการเรียนรู้",
      })
      return
    }

    if (!formData.description.trim()) {
      setModal({
        isOpen: true,
        type: "error",
        title: "ข้อมูลไม่ครบถ้วน",
        message: "กรุณากรอกคำอธิบายเส้นทางการเรียนรู้",
      })
      return
    }

    if (selectedCourses.length === 0) {
      setModal({
        isOpen: true,
        type: "error",
        title: "ไม่มีคอร์สในเส้นทาง",
        message: "กรุณาเพิ่มคอร์สอย่างน้อย 1 คอร์สในเส้นทางการเรียนรู้",
      })
      return
    }

    setModal({
      isOpen: true,
      type: "save",
      title: mode === "create" ? "สร้างเส้นทางการเรียนรู้" : "บันทึกการแก้ไข",
      message:
        mode === "create"
          ? `คุณต้องการสร้างเส้นทางการเรียนรู้ "${formData.name}" หรือไม่?`
          : `คุณต้องการบันทึกการแก้ไขเส้นทางการเรียนรู้ "${formData.name}" หรือไม่?`,
      onConfirm: handleSave,
    })
  }

  // Handle actual save
  const handleSave = () => {
    const learningPathData = {
      ...formData,
      courseIds: selectedCourses.map((course) => course.id),
      courseCount: selectedCourses.length,
    }

    console.log("Saving learning path:", learningPathData)

    // Simulate API call
    setTimeout(() => {
      setModal({
        isOpen: true,
        type: "success",
        title: mode === "create" ? "สร้างเส้นทางสำเร็จ" : "บันทึกสำเร็จ",
        message: mode === "create" ? "สร้างเส้นทางการเรียนรู้ใหม่เรียบร้อยแล้ว" : "บันทึกการแก้ไขเส้นทางการเรียนรู้เรียบร้อยแล้ว",
      })
    }, 500)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSaveClick()
  }

  // Close modal
  const closeModal = () => {
    console.log("Closing modal")
    setModal((prev) => ({ ...prev, isOpen: false }))
  }

  console.log("Current modal state:", modal)

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Header with title and action buttons */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-800">
            {mode === "create" ? "สร้างเส้นทางการเรียนรู้ใหม่" : "แก้ไขเส้นทางการเรียนรู้"}
          </h1>
          <div className="flex space-x-3">
            <Link href="/admin/learning-paths">
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                ยกเลิก
              </button>
            </Link>
            <button
              type="button"
              onClick={handleSaveClick}
              className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
            >
              {mode === "create" ? "สร้างเส้นทางการเรียนรู้" : "บันทึกการแก้ไข"}
            </button>
          </div>
        </div>

        <form id="learning-path-form" onSubmit={handleSubmit} className="space-y-0">
          {/* Basic Information Section */}
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">ข้อมูลพื้นฐาน</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ชื่อเส้นทางการเรียนรู้ *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                  placeholder="กรอกชื่อเส้นทางการเรียนรู้"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">สถานะ *</label>
                <div className="relative">
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData((prev) => ({ ...prev, status: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                  >
                    <option value="draft">แบบร่าง</option>
                    <option value="published">เผยแพร่</option>
                    <option value="archived">เก็บถาวร</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 pointer-events-none" />
                </div>
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">คำอธิบาย *</label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                placeholder="กรอกคำอธิบายเส้นทางการเรียนรู้"
                required
              />
            </div>
          </div>

          {/* Course Management Section */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-800">คอร์สในเส้นทางการเรียนรู้</h2>
              <span className="text-sm text-gray-500">{selectedCourses.length} คอร์ส</span>
            </div>

            {/* Selected Courses with Drag & Drop and Add Course integrated */}
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={selectedCourses.map((c) => c.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-3">
                  {selectedCourses.map((course) => (
                    <SortableCourseItem key={course.id} course={course} onRemove={removeCourse} />
                  ))}

                  {/* Add Course Section - Smaller dashed area */}
                  <div className="bg-gray-100 border-2 border-dashed border-gray-400 rounded-lg py-3 px-4">
                    <div className="flex justify-center">
                      <button
                        type="button"
                        onClick={() => setShowCourseDropdown(!showCourseDropdown)}
                        className="flex items-center gap-2 px-8 py-2 bg-white hover:bg-gray-50 rounded-lg text-gray-700 transition-colors font-medium border border-gray-300 min-w-[280px]"
                      >
                        <Plus className="h-4 w-4" />
                        เพิ่มคอร์สเรียนในเส้นทางการเรียน
                      </button>
                    </div>

                    {/* Course Dropdown */}
                    {showCourseDropdown && (
                      <div className="relative mt-3">
                        <div className="relative mb-2">
                          <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="ค้นหาคอร์ส..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-white"
                          />
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {availableCourses.length > 0 ? (
                            availableCourses.map((course) => (
                              <button
                                key={course.id}
                                type="button"
                                onClick={() => addCourse(course.id)}
                                className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                              >
                                <div className="font-medium text-black">{course.name || "ไม่มีชื่อคอร์ส"}</div>
                                <div className="text-sm text-gray-500 mt-1 line-clamp-2">{course.description}</div>
                              </button>
                            ))
                          ) : (
                            <div className="px-4 py-3 text-gray-500 text-center">
                              {searchTerm ? "ไม่พบคอร์สที่ค้นหา" : "ไม่มีคอร์สที่สามารถเพิ่มได้"}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </SortableContext>
            </DndContext>

            {/* Empty state when no courses */}
            {selectedCourses.length === 0 && (
              <div className="text-center py-12 text-gray-500 mb-6">
                <div className="text-lg mb-2">ยังไม่มีคอร์สในเส้นทางการเรียนรู้</div>
                <div className="text-sm">คลิกปุ่ม "เพิ่มคอร์สเรียนในเส้นทางการเรียน" เพื่อเริ่มสร้างเส้นทางการเรียนรู้</div>
              </div>
            )}
          </div>
        </form>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={modal.isOpen}
        onClose={closeModal}
        onConfirm={modal.onConfirm || closeModal}
        title={modal.title}
        message={modal.message}
        type={modal.type}
      />
    </>
  )
}
