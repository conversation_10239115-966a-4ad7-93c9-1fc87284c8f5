"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { Plus, X, ChevronDown } from "lucide-react"
import Link from "next/link"
import { getMedicalUsersData } from "@/data/allUsers"
import { learningPathsData } from "@/data/learningPaths"
import { updateUsersForPath, getAssignedPathsByUserId } from "@/data/assignedPaths"
import ConfirmationModal from "@/components/admin/confirmation-popup"

interface AssignEditorProps {
    mode: "create" | "edit"
    preSelectedStudentId?: string
    initialAssignedPaths?: string[]
}

interface SelectedPath {
    id: string
    name: string
    description: string
    courseCount: number
    learningMode: "sequential" | "freestyle"
}

interface ModalState {
    isOpen: boolean
    type: "save" | "delete" | "success" | "error"
    title: string
    message: string
    onConfirm?: () => void
}

export default function AssignEditor({ mode, preSelectedStudentId, initialAssignedPaths = [] }: AssignEditorProps) {
    const [selectedStudentId, setSelectedStudentId] = useState(preSelectedStudentId || "")
    const [selectedPaths, setSelectedPaths] = useState<SelectedPath[]>([])
    const [showStudentDropdown, setShowStudentDropdown] = useState(false)
    const [showPathDropdown, setShowPathDropdown] = useState(false)
    const [studentSearchTerm, setStudentSearchTerm] = useState("")
    const [pathSearchTerm, setPathSearchTerm] = useState("")
    const [modal, setModal] = useState<ModalState>({
        isOpen: false,
        type: "save",
        title: "",
        message: "",
    })

    // กรองเฉพาะ students
    const students = getMedicalUsersData.filter((user) => user.role === "student")

    // Function to load assigned paths for a student
    const loadStudentAssignedPaths = useCallback((studentId: string) => {
        const assignments = getAssignedPathsByUserId(studentId)
        const paths = assignments
            .map((assignment) => {
                const path = learningPathsData.find((p) => p.id === assignment.pathId)
                return path
                    ? {
                        id: path.id,
                        name: path.name,
                        description: path.description,
                        courseCount: path.courseCount,
                        learningMode: "sequential" as const, // Default to sequential, could be loaded from assignment data
                    }
                    : null
            })
            .filter(Boolean) as SelectedPath[]

        setSelectedPaths(paths)
    }, [])

    // Initialize selected paths from initial data (edit mode only)
    useEffect(() => {
        if (mode === "edit" && initialAssignedPaths.length > 0) {
            const paths = initialAssignedPaths
                .map((pathId) => {
                    const path = learningPathsData.find((p) => p.id === pathId)
                    return path
                        ? {
                            id: path.id,
                            name: path.name,
                            description: path.description,
                            courseCount: path.courseCount,
                            learningMode: "sequential" as const,
                        }
                        : null
                })
                .filter(Boolean) as SelectedPath[]

            setSelectedPaths(paths)
        }
    }, [mode, initialAssignedPaths])

    // Load assigned paths when student is selected (create mode only)
    useEffect(() => {
        if (mode === "create" && selectedStudentId && selectedStudentId !== preSelectedStudentId) {
            loadStudentAssignedPaths(selectedStudentId)
        }
    }, [selectedStudentId, mode, preSelectedStudentId, loadStudentAssignedPaths])

    // Handle student selection
    const handleStudentSelection = (studentId: string) => {
        setSelectedStudentId(studentId)
        setShowStudentDropdown(false)
        setStudentSearchTerm("")

        // Load existing assigned paths for this student
        loadStudentAssignedPaths(studentId)
    }

    // Get available students (searchable)
    const availableStudents = students.filter((student) =>
        `${student.firstname} ${student.lastname} ${student.email}`.toLowerCase().includes(studentSearchTerm.toLowerCase()),
    )

    // Get available paths (not already selected)
    const availablePaths = learningPathsData.filter(
        (path) =>
            path.status === "published" &&
            !selectedPaths.some((selected) => selected.id === path.id) &&
            path.name.toLowerCase().includes(pathSearchTerm.toLowerCase()),
    )

    // Get selected student info
    const selectedStudent = students.find((s) => s.id === selectedStudentId)

    // Add path to assignment
    const addPath = (pathId: string) => {
        const path = learningPathsData.find((p) => p.id === pathId)
        if (path) {
            setSelectedPaths((prev) => [
                ...prev,
                {
                    id: path.id,
                    name: path.name,
                    description: path.description,
                    courseCount: path.courseCount,
                    learningMode: "sequential", // Default to sequential
                },
            ])
            setShowPathDropdown(false)
            setPathSearchTerm("")
        }
    }

    // Remove path from assignment with confirmation
    const removePath = (pathId: string) => {
        const path = selectedPaths.find((p) => p.id === pathId)
        if (path) {
            setModal({
                isOpen: true,
                type: "delete",
                title: "ลบเส้นทางการเรียนรู้",
                message: `คุณต้องการลบเส้นทาง "${path.name}" ออกจากการมอบหมายหรือไม่?`,
                onConfirm: () => {
                    setSelectedPaths((prev) => prev.filter((p) => p.id !== pathId))
                    setModal({
                        isOpen: true,
                        type: "success",
                        title: "ลบเส้นทางสำเร็จ",
                        message: "ลบเส้นทางการเรียนรู้ออกจากการมอบหมายเรียบร้อยแล้ว",
                    })
                },
            })
        }
    }

    // Update learning mode for a specific path
    const updatePathLearningMode = (pathId: string, learningMode: "sequential" | "freestyle") => {
        setSelectedPaths((prev) => prev.map((path) => (path.id === pathId ? { ...path, learningMode } : path)))
    }

    // Handle save confirmation
    const handleSaveClick = () => {
        if (!selectedStudentId) {
            setModal({
                isOpen: true,
                type: "error",
                title: "ข้อมูลไม่ครบถ้วน",
                message: "กรุณาเลือกนักเรียน",
            })
            return
        }

        if (selectedPaths.length === 0) {
            setModal({
                isOpen: true,
                type: "error",
                title: "ไม่มีเส้นทางการเรียนรู้",
                message: "กรุณาเพิ่มเส้นทางการเรียนรู้อย่างน้อย 1 เส้นทาง",
            })
            return
        }

        const studentName = selectedStudent ? `${selectedStudent.firstname} ${selectedStudent.lastname}` : "นักเรียน"

        setModal({
            isOpen: true,
            type: "save",
            title: mode === "create" ? "สร้างการมอบหมาย" : "บันทึกการแก้ไข",
            message:
                mode === "create"
                    ? `คุณต้องการมอบหมายเส้นทางการเรียนรู้ ${selectedPaths.length} เส้นทางให้กับ ${studentName} หรือไม่?`
                    : `คุณต้องการบันทึกการแก้ไขการมอบหมายเส้นทางการเรียนรู้ให้กับ ${studentName} หรือไม่?`,
            onConfirm: handleSave,
        })
    }

    // Handle actual save
    const handleSave = () => {
        const assignmentData = {
            studentId: selectedStudentId,
            pathAssignments: selectedPaths.map((path) => ({
                pathId: path.id,
                learningMode: path.learningMode,
            })),
        }

        console.log("Saving assignment:", assignmentData)

        // Update assignments for each path
        selectedPaths.forEach((path) => {
            updateUsersForPath(path.id, [selectedStudentId])
        })

        // Simulate API call
        setTimeout(() => {
            setModal({
                isOpen: true,
                type: "success",
                title: mode === "create" ? "มอบหมายสำเร็จ" : "บันทึกสำเร็จ",
                message: mode === "create" ? "มอบหมายเส้นทางการเรียนรู้เรียบร้อยแล้ว" : "บันทึกการแก้ไขการมอบหมายเรียบร้อยแล้ว",
            })
        }, 500)
    }

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        handleSaveClick()
    }

    // Close modal
    const closeModal = () => {
        setModal((prev) => ({ ...prev, isOpen: false }))
    }

    return (
        <>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                {/* Header with save button */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h1 className="text-xl font-bold text-gray-800">
                        {mode === "edit" ? "แก้ไขเส้นทางการเรียนรู้" : "มอบหมายเส้นทางเรียนรู้"}
                    </h1>
                    <div className="flex space-x-3">
                        <Link href="/admin/assign">
                            <button
                                type="button"
                                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                ยกเลิก
                            </button>
                        </Link>
                        <button
                            onClick={handleSaveClick}
                            disabled={!selectedStudentId}
                            className={`px-4 py-2 rounded-md transition-colors font-semibold ${selectedStudentId
                                    ? "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white shadow-lg"
                                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                }`}
                        >
                            {mode === "create" ? "สร้างการมอบหมาย" : "บันทึกการแก้ไข"}
                        </button>
                    </div>
                </div>

                <form className="space-y-0">
                    {/* Student Selection Section */}
                    <div className="p-6 border-b border-gray-200">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">นักเรียน *</label>
                            <div className="relative max-w-md">
                                <button
                                    type="button"
                                    onClick={() => !preSelectedStudentId && setShowStudentDropdown(!showStudentDropdown)}
                                    disabled={!!preSelectedStudentId}
                                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10 text-left ${preSelectedStudentId ? "cursor-not-allowed opacity-75" : "cursor-pointer"
                                        }`}
                                >
                                    {selectedStudent ? `${selectedStudent.firstname} ${selectedStudent.lastname}` : "เลือกนักเรียน"}
                                </button>
                                {!preSelectedStudentId && (
                                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 pointer-events-none" />
                                )}

                                {showStudentDropdown && !preSelectedStudentId && (
                                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg">
                                        <div className="p-2">
                                            <input
                                                type="text"
                                                value={studentSearchTerm}
                                                onChange={(e) => setStudentSearchTerm(e.target.value)}
                                                placeholder="ค้นหานักเรียน..."
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]"
                                            />
                                        </div>
                                        <div className="max-h-60 overflow-y-auto">
                                            {availableStudents.length > 0 ? (
                                                availableStudents.map((student) => (
                                                    <button
                                                        key={student.id}
                                                        type="button"
                                                        onClick={() => handleStudentSelection(student.id)}
                                                        className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                                                    >
                                                        <div className="flex items-center space-x-3">
                                                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                                                <span className="text-gray-500 font-medium text-xs">
                                                                    {student.firstname.charAt(0)}
                                                                    {student.lastname.charAt(0)}
                                                                </span>
                                                            </div>
                                                            <div>
                                                                <div className="font-medium text-gray-900">
                                                                    {student.firstname} {student.lastname}
                                                                </div>
                                                                <div className="text-sm text-gray-500">{student.email}</div>
                                                            </div>
                                                        </div>
                                                    </button>
                                                ))
                                            ) : (
                                                <div className="px-4 py-3 text-gray-500 text-center">
                                                    {studentSearchTerm ? "ไม่พบนักเรียนที่ค้นหา" : "ไม่มีนักเรียนในระบบ"}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>


                        </div>
                    </div>

                    {/* Learning Paths Management Section */}
                    <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-lg font-semibold text-gray-800">เส้นทางการเรียนรู้</h2>
                            <span className="text-sm text-gray-500">{selectedPaths.length} เส้นทาง</span>
                        </div>

                        {/* Selected Paths */}
                        <div className="space-y-3">
                            {selectedPaths.map((path) => (
                                <div key={path.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                    <div className="flex items-center gap-4">
                                        <div className="flex-1">
                                            <h4 className="font-medium text-black">{path.name}</h4>
                                            <p className="text-sm text-gray-500 mt-1 line-clamp-2">{path.description}</p>
                                            <div className="flex items-center gap-2 mt-2">
                                                <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 border border-[#7586d3] text-[#293D97]">
                                                    {path.courseCount} คอร์ส
                                                </span>
                                            </div>
                                        </div>

                                        {/* Learning Mode Dropdown - positioned before delete button */}
                                        <div className="flex items-center gap-3">
                                            <div className="relative">
                                                <select
                                                    value={path.learningMode}
                                                    onChange={(e) =>
                                                        updatePathLearningMode(path.id, e.target.value as "sequential" | "freestyle")
                                                    }
                                                    className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-white pr-8 min-w-[140px]"
                                                >
                                                    <option value="sequential">เรียนตามลำดับ</option>
                                                    <option value="freestyle">ฟรีสไตล์</option>
                                                </select>
                                                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-3 w-3 pointer-events-none" />
                                            </div>

                                            <button
                                                type="button"
                                                onClick={() => removePath(path.id)}
                                                className="text-red-500 hover:text-red-700 p-1"
                                            >
                                                <X className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {/* Add Path Section */}
                            <div className="bg-gray-100 border-2 border-dashed border-gray-400 rounded-lg py-3 px-4">
                                <div className="flex justify-center">
                                    <button
                                        type="button"
                                        onClick={() => setShowPathDropdown(!showPathDropdown)}
                                        className="flex items-center gap-2 px-8 py-2 bg-white hover:bg-gray-50 rounded-lg text-gray-700 transition-colors font-medium border border-gray-300 min-w-[280px]"
                                    >
                                        <Plus className="h-4 w-4" />
                                        เพิ่มเส้นทางการเรียนรู้
                                    </button>
                                </div>

                                {/* Path Dropdown */}
                                {showPathDropdown && (
                                    <div className="relative mt-3">
                                        <div className="relative mb-2">
                                            <input
                                                type="text"
                                                value={pathSearchTerm}
                                                onChange={(e) => setPathSearchTerm(e.target.value)}
                                                placeholder="ค้นหาเส้นทางการเรียนรู้..."
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-white"
                                            />
                                        </div>

                                        <div className="bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                            {availablePaths.length > 0 ? (
                                                availablePaths.map((path) => (
                                                    <button
                                                        key={path.id}
                                                        type="button"
                                                        onClick={() => addPath(path.id)}
                                                        className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                                                    >
                                                        <div className="font-medium text-black">{path.name}</div>
                                                        <div className="text-sm text-gray-500 mt-1 line-clamp-2">{path.description}</div>
                                                        <div className="flex items-center gap-2 mt-2">
                                                            <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                                {path.courseCount} คอร์ส
                                                            </span>
                                                            <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                                เผยแพร่แล้ว
                                                            </span>
                                                        </div>
                                                    </button>
                                                ))
                                            ) : (
                                                <div className="px-4 py-3 text-gray-500 text-center">
                                                    {pathSearchTerm ? "ไม่พบเส้นทางที่ค้นหา" : "ไม่มีเส้นทางที่สามารถเพิ่มได้"}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Empty state when no paths */}
                        {selectedPaths.length === 0 && selectedStudentId && (
                            <div className="text-center py-12 text-gray-500 mb-6">
                                <div className="text-lg mb-2">นักเรียนคนนี้ยังไม่มีเส้นทางการเรียนรู้</div>
                                <div className="text-sm">คลิกปุ่ม "เพิ่มเส้นทางการเรียนรู้" เพื่อเริ่มมอบหมาย</div>
                            </div>
                        )}

                        {!selectedStudentId && (
                            <div className="text-center py-12 text-gray-500 mb-6">
                                <div className="text-lg mb-2">กรุณาเลือกนักเรียนก่อน</div>
                                <div className="text-sm">เลือกนักเรียนเพื่อดูและจัดการเส้นทางการเรียนรู้</div>
                            </div>
                        )}
                    </div>
                </form>
            </div>

            {/* Confirmation Modal */}
            <ConfirmationModal
                isOpen={modal.isOpen}
                onClose={closeModal}
                onConfirm={modal.onConfirm || closeModal}
                title={modal.title}
                message={modal.message}
                type={modal.type}
            />
        </>
    )
}
