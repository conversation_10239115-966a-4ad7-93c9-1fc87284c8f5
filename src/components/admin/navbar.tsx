"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Bell, LogOut, Search, ChevronDown, User, Settings, HelpCircle } from "lucide-react"

interface NavbarProps {
  isCollapsed: boolean
}

export default function Navbar({ isCollapsed }: NavbarProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const notificationRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Mock notifications
  const notifications = [
    { id: 1, title: "มีผู้ใช้ใหม่ลงทะเบียน", time: "5 นาทีที่แล้ว", read: false },
    { id: 2, title: "มีการลงทะเบียนคอร์สใหม่", time: "1 ชั่วโมงที่แล้ว", read: false },
    { id: 3, title: "อัพเดทระบบเสร็จสมบูรณ์", time: "1 วันที่แล้ว", read: true },
  ]

  return (
    <header
      className={`fixed top-0 right-0 left-0 md:left-${isCollapsed ? "20" : "64"} h-[7.2vh] bg-white shadow-sm z-30 transition-all duration-300 ease-in-out`}
      style={{ left: 0, [isCollapsed ? "paddingLeft" : "paddingLeft"]: isCollapsed ? "5rem" : "16rem" }}
    >
      <div className="flex items-center justify-between h-full px-4 md:px-6">
        <div className="flex items-center">

          <div className="hidden md:flex relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="ค้นหา..."
              className="pl-10 pr-4 py-1.5 rounded-md border  bg-gray-50 border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#2FBCC1] focus:border-transparent text-sm h-[4vh] w-[50vh]"
            />
          </div>
        </div>

        <div className="flex items-center space-x-1">
          {/* Notifications */}
          <div className="relative" ref={notificationRef}>
            <button
              className="p-2 text-gray-600 hover:text-[#004C41] hover:bg-gray-100 rounded-full transition-colors relative"
              onClick={() => setShowNotifications(!showNotifications)}
              aria-label="Notifications"
            >
              <Bell size={20} />
              {notifications.some((n) => !n.read) && (
                <span className="absolute top-1.5 right-1.5 w-2 h-2 bg-red-500 rounded-full"></span>
              )}
            </button>

            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <h3 className="font-medium text-gray-800">การแจ้งเตือน</h3>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`px-4 py-3 hover:bg-gray-50 border-l-2 ${notification.read ? "border-transparent" : "border-[#004C41]"}`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className={`text-sm ${notification.read ? "text-gray-600" : "text-gray-800 font-medium"}`}>
                            {notification.title}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                        </div>
                        {!notification.read && <span className="w-2 h-2 bg-[#004C41] rounded-full"></span>}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="px-4 py-2 border-t border-gray-100 text-center">
                  <button className="text-sm text-[#004C41] hover:underline">ดูการแจ้งเตือนทั้งหมด</button>
                </div>
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative" ref={userMenuRef}>
            <button
              className="flex items-center space-x-1 p-1.5 hover:bg-gray-100 rounded-md transition-colors"
              onClick={() => setShowUserMenu(!showUserMenu)}
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#004C41] to-[#2FBCC1] flex items-center justify-center text-white">
                <span className="text-sm font-medium">J</span>
              </div>
              <ChevronDown size={16} className="text-gray-500" />
            </button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="font-medium text-gray-800">Jaruphat Kiatchaisiriporn</p>
                  <p className="text-sm text-gray-500"><EMAIL></p>
                </div>
                <div className="py-1">
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <User size={16} />
                    <span>โปรไฟล์</span>
                  </button>
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Settings size={16} />
                    <span>ตั้งค่า</span>
                  </button>
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <HelpCircle size={16} />
                    <span>ช่วยเหลือ</span>
                  </button>
                </div>
                <div className="border-t border-gray-100 pt-1 mt-1">
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-50">
                    <LogOut size={16} />
                    <span>ออกจากระบบ</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

