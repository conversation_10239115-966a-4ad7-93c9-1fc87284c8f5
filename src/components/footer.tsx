"use client";
import { FaLine, FaPhoneAlt, FaFacebook } from "react-icons/fa";
import { MdEmail } from "react-icons/md";

const Footer = () => {
  return (
    <div className="bg-[#164A7E] bg-opacity-50 py-8 backdrop-blur-2xl">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-4  ">
        {/* Left Grid: Logos and Text */}
        <div className="text-center md:text-left ">
          {/* Logos */}
          <div className="flex flex-wrap justify-center md:justify-start items-center gap-4 mb-6">
            <img
              src="/e-med/img/164137.png"
              alt="synphaet Logo"
              className="w-36"
            />
            <img src="/e-med/img/bu_logo.png" alt="BU Logo" className="" />
            {/* <img src="/e-med/img/depa_logo.png" alt="Depa Logo" className="" /> */}
            {/* <img
              src="/e-med/img/digital_department_logo.png"
              alt="Digital Department Logo"
              className=""
            /> */}
          </div>
          {/* Text Under Logos */}
          {/* <div className="text-cyan-400 text-sm">
            <p className="text-lg sm:text-2xl bg-gradient-to-r from-[#05ACFE] to-[#FFFFFF] inline-block text-transparent bg-clip-text">
              Print("Unleash Your Imagination")
            </p>
          </div> */}
          <div className="text-white text-xs sm:text-sm mt-2">
            <p>Copyright © 2023 Center of Specialty Innovation</p>
          </div>
        </div>

        {/* Right Grid: Contact and Additional Information */}
        <div className="grid grid-cols-2 gap-4 text-white text-xs sm:text-sm ibm-plex-sans-thai-regular px-4">
          {/* Left Column */}
          <div>
            <h2 className="text-base sm:text-lg mb-2 ibm-plex-sans-thai-semibold">
              ติดต่อเรา
            </h2>
            <p className="flex items-center mt-2">
              <FaPhoneAlt className="mr-2" />
              02-793-5099
            </p>
            <p className="flex items-center mt-2">
              <FaLine className="mr-2" />
              @SynphaetHospital
            </p>
            <p className="flex items-center mt-2">
              <FaFacebook className="mr-2" />
              Synphaet Hospital
            </p>
            <p className="flex items-center mt-2">
              <MdEmail className="mr-2" />
              <span className="hidden md:inline-block">
                contact
                <br className="hidden md:inline" />
                @synphaet.co.th
              </span>
              <span className="block md:hidden">
                contact
                <br />
                @synphaet.co.th
              </span>
            </p>
          </div>

          {/* Right Column */}
          <div className="sm:pl-5 md:pl-2 lg:pl-0">
            <h2 className="text-base sm:text-lg ibm-plex-sans-thai-semibold mb-2">
              เรียนรู้เพิ่มเติม
            </h2>
            <p className="mt-2">เกี่ยวกับเรา</p>
            <p className="mt-2">นโยบายความเป็นส่วนตัว</p>
            <p className="mt-2">เงื่อนไขและการใช้งาน</p>
            <p className="mt-2">ความช่วยเหลือและแอปสนับสนุน</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
