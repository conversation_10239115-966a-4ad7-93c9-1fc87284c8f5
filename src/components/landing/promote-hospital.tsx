"use client";
import React, { ReactNode } from "react";

interface Props {
  children: ReactNode;
}

const PromoteHospital: React.FC<Props> = ({ children }) => {
  return (
    <div
      className="bg-cover bg-center min-h-screen flex flex-col justify-between"
      style={{ backgroundImage: "url(/e-med/img/hospital_bg.png)", opacity: 0.90 }}
    >
      {/* Header Section */}
      <div className="relative flex items-center justify-center lg:justify-end px-6 sm:px-12 md:px-20 lg:px-28 h-[50vh]">
        <div className="p-6 sm:p-8 rounded-md max-w-xl sm:max-w-2xl lg:max-w-3xl ibm-plex-sans-thai-regular text-center lg:text-left">
          <h1 className="text-white text-3xl sm:text-4xl lg:text-5xl ibm-plex-sans-thai-semibold">
            {"เรียนรู้การแพทย์ออนไลน์"}
          </h1>
          <p className="text-white mt-4 text-lg sm:text-xl lg:text-2xl">
            เสริมทักษะและ{" "}
            <span className="bg-gradient-to-r from-[#283C97] to-[#098770] inline-block text-transparent bg-clip-text ibm-plex-sans-thai-semibold">
              เปิดประตูสู่ความรู้ใหม่
            </span>
            <br />
            กับหลักสูตร E-learning การแพทย์เพื่อสุขภาพ
            <br />
            สำหรับผู้เรียนทุกระดับ
          </p>
          <div className="flex justify-center lg:justify-start mt-8">
            <a
              href="#"
              className="bg-[#4F62A8] hover:bg-[#2CBCA0] text-white py-2 px-4 sm:py-3 sm:px-6 rounded-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 lg:justify-items-start"
            >
              เริ่มเรียนเลย
            </a>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      {children}
    </div>
  );
};

export default PromoteHospital;
