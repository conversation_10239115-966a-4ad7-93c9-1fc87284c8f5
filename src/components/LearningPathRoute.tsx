"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"


import { Clock, CheckCircle, Search, Target, TrendingUp, Route, ArrowLeft } from "lucide-react"
import LearningPathsCard from "./learming/LearningPathCard"
import { learningPathsData } from "@/data/learningPaths"
import { getPathsAssignedToUser } from "@/data/assignedPaths"
import { getUserProgress } from "@/data/userProgress"

const LearningPathsRoute = () => {
  const router = useRouter()
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [userPathways, setUserPathways] = useState<any[]>([])
  const [filteredPathways, setFilteredPathways] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  // Get current user ID from localStorage
  useEffect(() => {
    const userId = localStorage.getItem("userId")
    setCurrentUserId(userId)
  }, [])

  // Load user's assigned pathways
  useEffect(() => {
    if (!currentUserId) return

    const loadUserPathways = () => {
      try {
        // Get paths assigned to this user
        const assignedPathIds = getPathsAssignedToUser(currentUserId)

        // Filter only assigned paths
        const assignedPaths = learningPathsData.filter((path) => assignedPathIds.includes(path.id))

        // Get user progress
        const userProgress = getUserProgress(currentUserId)

        // Calculate completion stats for each path
        const pathsWithStats = assignedPaths.map((path) => {
          // Count completed courses
          const completedCourses = path.courseIds.filter((courseId) => {
            const progress = userProgress.find((p) => p.courseId === courseId)
            return progress?.status === "completed"
          }).length

          // Calculate progress percentage
          const progressPercentage = Math.round((completedCourses / path.courseIds.length) * 100)

          return {
            ...path,
            completedCourses,
            totalCourses: path.courseIds.length,
            progressPercentage,
          }
        })

        setUserPathways(pathsWithStats)
        setFilteredPathways(pathsWithStats)
        setLoading(false)
      } catch (error) {
        console.error("Error loading pathways:", error)
        setLoading(false)
      }
    }

    loadUserPathways()
  }, [currentUserId])

  // Filter pathways based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredPathways(userPathways)
      return
    }

    const filtered = userPathways.filter(
      (pathway) =>
        pathway.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pathway.description.toLowerCase().includes(searchTerm.toLowerCase()),
    )

    setFilteredPathways(filtered)
  }, [userPathways, searchTerm])

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f9fafb] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดเส้นทางการเรียน...</p>
        </div>
      </div>
    )
  }

  // Calculate statistics
  const completedPathsCount = userPathways.filter((p) => p.progressPercentage === 100).length
  const inProgressPathsCount = userPathways.filter((p) => p.progressPercentage > 0 && p.progressPercentage < 100).length
  const averageHours =
    userPathways.length > 0
      ? Math.round(userPathways.reduce((acc, p) => acc + (p.estimatedDuration || 0), 0) / userPathways.length)
      : 0

  return (
    <div className="min-h-screen bg-[#f9fafb] w-full">
      <div className="flex w-full">
        {/* Main Content */}
        <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
          <div className="w-full p-6">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center mb-2">
                <button
                  onClick={() => router.push("/profile/dashboard")}
                  className="mr-2 p-1 rounded-full hover:bg-gray-200"
                >
                  <ArrowLeft size={20} />
                </button>
                <h1 className="text-2xl font-bold text-gray-800">เส้นทางการเรียนเพื่อรับใบประกาศนียบัตร</h1>
              </div>
              <p className="text-gray-600">เลือกเส้นทางการเรียนที่เหมาะสมกับคุณ เพื่อพัฒนาความรู้และรับใบประกาศนียบัตรในสาขาที่สนใจ</p>
            </div>

            {/* Search */}
            <div className="mb-8 bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="text"
                      placeholder="ค้นหาเส้นทางการเรียน..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#008268] focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-[#008268]/10 rounded-lg">
                    <Route className="h-6 w-6 text-[#008268]" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">เส้นทางทั้งหมด</p>
                    <p className="text-2xl font-bold text-gray-900">{userPathways.length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">เส้นทางที่เรียนจบ</p>
                    <p className="text-2xl font-bold text-gray-900">{completedPathsCount}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">กำลังเรียน</p>
                    <p className="text-2xl font-bold text-gray-900">{inProgressPathsCount}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">เวลาเฉลี่ย</p>
                    <p className="text-2xl font-bold text-gray-900">{averageHours} ชม.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Pathway Cards */}
            {filteredPathways.length === 0 ? (
              <div className="text-center py-16">
                <div className="mb-6">
                  <Target size={64} className="mx-auto text-gray-300" />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">ไม่พบเส้นทางการเรียนที่ตรงกับการค้นหา</h3>
                <p className="text-gray-500 mb-6">ลองปรับเปลี่ยนคำค้นหาเพื่อดูเส้นทางการเรียนอื่นๆ</p>
                <button
                  onClick={() => setSearchTerm("")}
                  className="px-6 py-3 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
                >
                  ล้างการค้นหา
                </button>
              </div>
            ) : (
              <LearningPathsCard filteredPathways={filteredPathways} accordionMode={false} defaultExpanded={[]} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LearningPathsRoute
