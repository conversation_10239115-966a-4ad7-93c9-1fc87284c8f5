"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Book<PERSON><PERSON>, Award, Route, HomeIcon } from "lucide-react"

const DashboardSidebar = () => {
  const pathname = usePathname()

  const isActive = (href: string) => {
    if (href === "/profile/dashboard") {
      return pathname === "/profile/dashboard"
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      <div className="w-[60px] fixed h-screen bg-[#008268] flex flex-col items-center py-6 z-30">
        <nav className="flex flex-col items-center gap-8 flex-grow">
          <Link
            href="/profile/dashboard"
            className={`text-white p-3 rounded-xl transition-all duration-200 transform active:scale-95 active:translate-y-1 ${
              isActive("/profile/dashboard") ? "bg-[#006e58]" : "hover:bg-[#006e58]"
            }`}
          >
            <BookOpen size={24} />
          </Link>
          <Link
            href="/profile/dashboard"
            className={`text-white p-3 rounded-xl transition-all duration-200 transform active:scale-95 active:translate-y-1 ${
              isActive("/profile/dashboard") ? "bg-[#006e58]" : "hover:bg-[#006e58]"
            }`}
          >
            <HomeIcon size={24} />
          </Link>
          <Link
            href="/profile/certificate-pathways"
            className={`text-white p-3 rounded-xl transition-all duration-200 transform active:scale-95 active:translate-y-1 ${
              isActive("/profile/certificate-pathways") ? "bg-[#006e58]" : "hover:bg-[#006e58]"
            }`}
          >
            <Route size={24} />
          </Link>
          <Link
            href="/profile/dashboard/my-courses"
            className={`text-white p-3 rounded-xl transition-all duration-200 transform active:scale-95 active:translate-y-1 ${
              isActive("/profile/dashboard/my-courses") ? "bg-[#006e58]" : "hover:bg-[#006e58]"
            }`}
          >
            <BookOpen size={24} />
          </Link>
        </nav>
      </div>
    </>
  )
}

export default DashboardSidebar
