"use client"

import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react"
import { Popover, Transition } from "@headlessui/react"
import Link from "next/link"
import { Fragment } from "react"
import { FiMenu, FiX, FiUser, FiLogOut, FiSettings } from "react-icons/fi"
import { useRouter, usePathname } from "next/navigation"
import { getMedicalUsersData } from "@/data/allUsers"

// Types
interface User {
  id: string
  firstname: string
  lastname: string
  email: string
}

interface NavbarState {
  isLearning: boolean
  isLoggedIn: boolean
  currentUser: User | null
  isLoading: boolean
}

export default function Navbar() {
  const [state, setState] = useState<NavbarState>({
    isLearning: false,
    isLoggedIn: false,
    currentUser: null,
    isLoading: true,
  })

  const router = useRouter()
  const pathname = usePathname()

  // Check if current path is learning page
  const isLearningPage = useMemo(() => {
    return pathname?.includes("/learning") || false
  }, [pathname])

  // Check login status and get user data
  const checkLoginStatus = useCallback(() => {
    try {
      const userId = localStorage.getItem("userId")

      if (userId) {
        const user = getMedicalUsersData.find((user) => user.id === userId)

        setState((prev) => ({
          ...prev,
          isLoggedIn: true,
          currentUser: user || null,
          isLoading: false,
        }))
      } else {
        setState((prev) => ({
          ...prev,
          isLoggedIn: false,
          currentUser: null,
          isLoading: false,
        }))
      }
    } catch (error) {
      console.error("Error checking login status:", error)
      setState((prev) => ({
        ...prev,
        isLoggedIn: false,
        currentUser: null,
        isLoading: false,
      }))
    }
  }, [])

  // Handle logout
  const handleLogout = useCallback(() => {
    try {
      localStorage.removeItem("userId")
      setState((prev) => ({
        ...prev,
        isLoggedIn: false,
        currentUser: null,
      }))
      router.push("/login")
    } catch (error) {
      console.error("Error during logout:", error)
    }
  }, [router])

  // Update learning page status
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      isLearning: isLearningPage,
    }))
  }, [isLearningPage])

  // Check login status on mount
  useEffect(() => {
    checkLoginStatus()
  }, [checkLoginStatus])

  // Get user initials
  const userInitials = useMemo(() => {
    if (!state.currentUser) return ""
    const firstInitial = state.currentUser.firstname?.charAt(0) || ""
    const lastInitial = state.currentUser.lastname?.charAt(0) || ""
    return `${firstInitial}${lastInitial}`
  }, [state.currentUser])

  // Don't render navbar if on learning page or user not logged in
  if (state.isLearning || !state.isLoggedIn || state.isLoading) {
    return null
  }

  return (
    <header className="fixed top-0 left-0 w-full z-50 bg-white shadow-md">
      <nav className="flex items-center justify-between py-1 px-6 lg:px-8">
        {/* Logo */}
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5">
            <span className="sr-only">E-MED Learning</span>
            <img
              alt="E-MED Learning Logo"
              src="https://pbs.twimg.com/profile_images/1326823104052486145/UTZqGpOl_400x400.jpg"
              className="h-[4rem] w-auto"
            />
          </Link>
        </div>

        {/* Desktop Menu */}
        <div className="hidden lg:flex lg:flex-1 lg:justify-end">
          <div className="relative">
            <Popover className="relative">
              {({ open }) => (
                <>
                  <Popover.Button className="flex items-center focus:outline-none">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#004C41] to-[#2FBCC1] flex items-center justify-center text-white">
                      <span className="text-sm font-medium">{userInitials}</span>
                    </div>
                  </Popover.Button>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <Popover.Panel className="absolute right-0 z-10 mt-2 w-64 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <div className="p-4 border-b border-gray-100">
                        <p className="font-medium text-gray-800">
                          {state.currentUser?.firstname} {state.currentUser?.lastname}
                        </p>
                        <p className="text-sm text-gray-500">{state.currentUser?.email}</p>
                      </div>
                      <div className="py-1">
                        <Link
                          href="/profile/dashboard"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <FiUser className="mr-2" />
                          ดูโปรไฟล์ของฉัน
                        </Link>
                        <Link
                          href="/profile"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <FiSettings className="mr-2" />
                          แก้ไขโปรไฟล์
                        </Link>
                        <button
                          onClick={handleLogout}
                          className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                        >
                          <FiLogOut className="mr-2" />
                          ออกจากระบบ
                        </button>
                      </div>
                    </Popover.Panel>
                  </Transition>
                </>
              )}
            </Popover>
          </div>
        </div>

        {/* Mobile Menu */}
        <Popover className="lg:hidden">
          {({ open }) => (
            <>
              <Popover.Button className="inline-flex items-center justify-center p-2 text-gray-700 rounded-md focus:outline-none">
                <span className="sr-only">Open menu</span>
                {open ? (
                  <FiX className="h-6 w-6" aria-hidden="true" />
                ) : (
                  <FiMenu className="h-6 w-6" aria-hidden="true" />
                )}
              </Popover.Button>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Popover.Panel
                  focus
                  className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 p-4 bg-white shadow-md rounded-lg w-[90%] max-w-7xl"
                >
                  <div className="flex items-center justify-between">
                    <Link href="/" className="-m-1.5 p-1.5">
                      <span className="sr-only">E-MED Learning</span>
                      <img
                        alt="E-MED Learning Logo"
                        src="https://pbs.twimg.com/profile_images/1326823104052486145/UTZqGpOl_400x400.jpg"
                        className="h-[4rem] w-auto"
                      />
                    </Link>
                    <Popover.Button className="p-2 text-gray-700 rounded-md focus:outline-none">
                      <span className="sr-only">Close menu</span>
                      <FiX className="h-6 w-6" aria-hidden="true" />
                    </Popover.Button>
                  </div>
                  <div className="mt-4 space-y-4">
                    <Link href="/courses" className="block text-sm font-semibold text-gray-900">
                      คอร์สเรียน
                    </Link>
                    <Link href="#" className="block text-sm font-semibold text-gray-900">
                      เกี่ยวกับ
                    </Link>
                    <Link href="/profile/dashboard" className="block text-sm font-semibold text-gray-900">
                      ดูโปรไฟล์ของฉัน
                    </Link>
                    <Link href="/profile/settings" className="block text-sm font-semibold text-gray-900">
                      แก้ไขโปรไฟล์
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left text-sm font-semibold text-red-600"
                    >
                      ออกจากระบบ
                    </button>
                  </div>
                </Popover.Panel>
              </Transition>
            </>
          )}
        </Popover>
      </nav>
    </header>
  )
}
