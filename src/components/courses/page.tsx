"use client"

import { useState, useEffect } from "react"
import { getCoursesData } from "@/data/allCourses"
import type { CourseType } from "@/types/courses"
import { useParams } from "next/navigation"
import CourseTitle from "@/components/coursecompo/CourseTitle"
import CourseDetails from "../coursecompo/CourseDetail"
import RecommendedCourses from "../coursecompo/randomCourse"
import Link from "next/link"
import Footer from "../footer"
import CourseNav from "../coursecompo/Navtab"
import CourseDetailsCard from "../coursecompo/CourseDetailBar"

export default function AllCoursesPage() {
    const coursesData = getCoursesData;
    const [searchQuery, setSearchQuery] = useState("");
    const [filteredCourses, setFilteredCourses] = useState(coursesData);
    
    const randomCourses = filteredCourses.sort(() => 0.5 - Math.random()).slice(0, 3);
    useEffect(() => {
        //console.log("All Courses:", coursesData);
    }, []);


    const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
        const query = event.target.value.toLowerCase();
        setSearchQuery(query);

        const results = coursesData.filter((course) => {
            const courseText = `
        ${course.name.toLowerCase()} 
        ${course.description.toLowerCase()} 
        ${course.level.toLowerCase()} 
        ${course.teacher.name.toLowerCase()} 
        ${course.lesson.length} 
        ${Math.round(course.time / 3600)} 
      `;

            return courseText.includes(query);
        });

        setFilteredCourses(results);
    };

    const params = useParams()
    const [course, setCourse] = useState<CourseType | null>(null)
    const level = course?.level || "เบื้องต้น";

    useEffect(() => {
        if (params && params.id) {
            const selectedCourse = getCoursesData.find((course) => course.id === params.id)
            setCourse(selectedCourse || null)
        }
    }, [params])

    if (!course) {
        return (
            <main className="min-h-screen bg-[#F0FCFF] flex items-center justify-center">
                <p className="text-lg text-gray-800">Loading course details...</p>
            </main>
        )
    }

    return (
        <main className="min-h-screen bg-white">
            <CourseTitle 
                name={course.name || "Untitled Course"} 
                teacher={course.teacher} 
                courseId={course.id || "unknown-id"} 
                coverImage={course.coverImage || "/default-cover.jpg"} 
            />
            {/* Course Details */}
            <CourseDetailsCard course={course} level={level} />

            <div className="max-w-5xl mx-auto px-4 py-8">
            <CourseNav
                sections={[
                    { id: 'about', label: 'เกี่ยวกับ' },
                    { id: 'instruction', label: 'คำแนะนำในการเรียน' },
                ]}
            />
            </div>
            <div id="about">
            <CourseDetails
                courseId={course.id}
                description={course.description}
                instruction={course.instruction}
                modules={course.lesson.map((lesson: { name: any; description: any} ) => ({
                    title: lesson.name,
                    description: lesson.description
                }))}
                lessons={course.lesson} 
                certify={course.certify} />
                </div>


        </main>
    )
}