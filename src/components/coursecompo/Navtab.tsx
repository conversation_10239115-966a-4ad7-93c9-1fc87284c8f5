interface CourseNavProps {
    sections: { id: string; label: string }[];
}

export default function CourseNav({ sections }: CourseNavProps) {
    const scrollToSection = (id: string) => {
        const section = document.getElementById(id);
        if (section) {
            section.scrollIntoView({ behavior: "smooth" });
        }
    };

    return (
        <div className="border-b -mb-10">
            <nav className="hidden md:flex gap-8">
                {sections.map((section) => (
                    <button
                        key={section.id}
                        onClick={() => scrollToSection(section.id)}
                        className="px-4 py-2 text-md font-medium text-gray-600 
                                   hover:bg-[#E4F2F0] hover:text-[#008268] 
                                   rounded-lg transition duration-200"
                    >
                        {section.label}
                    </button>
                ))}
            </nav>
        </div>
    );
}
