import { BookOpen, Video, FileQuestion } from "lucide-react";
import { getCoursesData } from "@/data/allCourses"; // เปลี่ยน path ตามโครงสร้างโปรเจคของคุณ
import { CourseType } from "@/types/courses";

interface SidebarProps {
  courseId: string;
}

export default function Sidebar({ courseId }: SidebarProps) {
  // ค้นหาคอร์สจาก courseId
  const course = getCoursesData.find((course) => course.id === courseId);

  if (!course) {
    return <div>Course not found</div>; // หากไม่พบคอร์ส
  }

  // คำนวณจำนวนบทเรียน, วิดีโอ, และคำถาม
  const lessonsCount = course.lesson.length;
  const videosCount = course.lesson.reduce(
    (count, lesson) => count + lesson.content.filter((item) => item.typecontent === "video").length,
    0
  );
  const questionsCount = course.lesson.reduce(
    (count, lesson) => count + lesson.content.filter((item) => item.typecontent === "pdf").length,
    0
  );

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border-gray-200 border md:mb-3 lg:mb-0">
      {/* Default Vertical Layout (mobile and lg+) */}
      <div className="flex flex-col space-y-6 md:hidden lg:flex lg:space-y-6">
        <div className="flex items-center gap-3">
          <BookOpen className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">Lessons</h3>
            <p className="text-sm text-gray-500">{lessonsCount} Lessons</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Video className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">Clips & Videos</h3>
            <p className="text-sm text-gray-500">{videosCount} Clips & Videos</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <FileQuestion className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">Quiz & Questions</h3>
            <p className="text-sm text-gray-500">{questionsCount} Questions</p>
          </div>
        </div>
      </div>

      {/* Tablet Only: Horizontal Layout */}
      <div className="hidden md:grid md:grid-cols-3 md:gap-8 lg:hidden">
        <div className="flex items-center gap-3">
          <BookOpen className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">Lessons</h3>
            <p className="text-sm text-gray-500">{lessonsCount} Lessons</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Video className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">Clips & Videos</h3>
            <p className="text-sm text-gray-500">{videosCount} Clips & Videos</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <FileQuestion className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">Quiz & Questions</h3>
            <p className="text-sm text-gray-500">{questionsCount} Questions</p>
          </div>
        </div>
      </div>
    </div>
  );
}