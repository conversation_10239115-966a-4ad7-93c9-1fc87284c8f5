import { UserIcon } from "@heroicons/react/16/solid"

interface CourseTitleProps {
  name: string
  teacher: {
    name: string
    description: string
    avatar?: string
  }
  courseId: string
  coverImage: string
}

export default function CourseTitle({ name, teacher, courseId, coverImage }: CourseTitleProps) {
  return (
    <div className="relative">
      <div
        className="h-[50vh] sm:h-[30vh] md:h-[40vh] ipad-pro:h-[40vh] ipad-air-landscape:h-[50vh] ipad-mini-landscape:h-[50vh] lg:h-[60vh] bg-cover bg-center"
        style={{
          backgroundImage: `url(${coverImage})`,
        }}
      >
        <div className="absolute inset-0 h-full bg-gradient-to-r from-black/90 to-transparent">
          <div className="max-w-5xl mx-auto px-4 h-full">
            {/* Left-aligned content container */}
            <div className="max-w-2xl pt-[15vh] sm:pt-16 md:pt-[8vh] lg:pt-[28vh] ipad-pro:pt-[18vh]">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4 sm:mb-6">{name}</h1>

              <div className="flex items-start space-x-4 mb-6 sm:mb-8">
                <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 border-white overflow-hidden bg-gray-200 shrink-0">
                  {teacher.avatar ? (
                    <img
                      src={teacher.avatar || "/placeholder.svg"}
                      alt={`${teacher.name} Avatar`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-600">
                      <UserIcon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                  )}
                </div>
                <div className="text-white">
                  <p className="text-sm sm:text-base opacity-90">Instructor: {teacher.name}</p>
                  <p className="text-xs sm:text-sm opacity-80">{teacher.description}</p>
                </div>
              </div>

              <button
                onClick={() => (window.location.href = `/e-med/learning/${courseId}`)}
                className="bg-[#7B8FF7] hover:bg-[#6B7FE7] text-white px-6 sm:px-8 py-2.5 sm:py-3 rounded-lg transition-colors duration-300 text-base sm:text-lg font-medium"
              >
                เริ่มเรียนบทเรียน
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

