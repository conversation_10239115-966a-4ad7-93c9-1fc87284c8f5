"use client";
import { useEffect, useState } from "react";

export default function UserActivityTracker({
    onPlay,
}: {
    onPlay: (status: boolean) => void;
}) {
    const [status, setStatus] = useState("Active");
    // const router = useRouter();

    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.hidden) {
                setStatus("Tab is inactive");
            } else {
                setStatus("Tab is active");
            }
        };

        const handleFocus = () => {
            if (!document.hidden) {
                setStatus("Active");
            }
        };

        const handleBlur = () => {
            // Ensure blur events are not triggered by iframe interactions
            if (document.activeElement?.tagName === "IFRAME") {
                return;
            }
            setStatus("Window is not focused");
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);
        window.addEventListener("focus", handleFocus);
        window.addEventListener("blur", handleBlur);

        return () => {
            document.removeEventListener("visibilitychange", handleVisibilityChange);
            window.removeEventListener("focus", handleFocus);
            window.removeEventListener("blur", handleBlur);
        };
    }, []);

    useEffect(() => {
        if (status !== "Active") {
            (document.getElementById("my_modal_5") as HTMLDialogElement)?.showModal();
            onPlay(false);
        }
    }, [status]);

    return (
        <div>
            <dialog id="my_modal_5" className="modal modal-bottom sm:modal-middle">
                <div className="modal-box bg-white">
                    <h3 className="font-bold text-lg">Warning!</h3>
                    <p className="py-4">
                        You are currently not active on the page. Please click the button to continue.
                    </p>
                    <div className="modal-action">
                        <form method="dialog">
                            <button
                                className="btn bg-white text-gray-800 hover:text-white hover:bg-[#2FBCC1] border-[#164A7E]"
                                onClick={() => {
                                    (document.getElementById("my_modal_5") as HTMLDialogElement)?.close();
                                    onPlay(true);
                                }}
                            >
                                Continue
                            </button>
                        </form>
                    </div>
                </div>
            </dialog>
        </div>
    );
}
