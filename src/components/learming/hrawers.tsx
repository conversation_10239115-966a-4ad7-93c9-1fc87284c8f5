"use client"

import { useEffect, useState } from "react"
import type { ContentType } from "@/types/courses"
// Define lessonType here if not exported from "@/types/courses"
type lessonType = {
  id: string
  name: string
  time: number
  content: contentType[]
}
type contentType = {
  id: string
  name: string
  typecontent: string
  time: number
}
import {
  Clock,
  BookOpen,
  ChevronDown,
  ArrowLeft,
  User,
  Video,
  FileText,
  X,
  CheckCircle,
  Lock,
  Menu,
} from "lucide-react"

interface HrawersLearmingProps {
  courses: any
  menuOpen: {
    opened: boolean
    onMenuOpen: (menuOpen: boolean) => void
  }
  select: {
    selectId: {
      lessonId: string
      contentId: string
    }
    setSelectId: (selectId: { lessonId: string; contentId: string }) => void
  }
  userProgress?: {
    completedContents?: string[]
    completedLessons?: string[]
    allLessonsCompleted?: boolean
  }
  hasFinalExam?: boolean
  userId?: string
}

export default function HrawersLearming({
  courses,
  select,
  menuOpen,
  userProgress = {}, // เพิ่ม prop สำหรับความก้าวหน้าของผู้ใช้
  hasFinalExam = false, // เพิ่ม prop สำหรับตรวจสอบว่ามีข้อสอบท้ายบทหรือไม่
  userId = "",
}: HrawersLearmingProps) {
  const [expandedLessons, setExpandedLessons] = useState<Record<string, boolean>>({})

  // ฟังก์ชันสำหรับสร้าง key ที่ใช้ใน localStorage โดยรวม userId
  const getStorageKey = (key: string) => {
    return `${key}_${userId}`
  }

  // Initialize expanded state for the selected lesson
  useEffect(() => {
    if (select.selectId.lessonId) {
      setExpandedLessons((prev) => ({
        ...prev,
        [select.selectId.lessonId]: true,
      }))
    }
  }, [select.selectId.lessonId])

  const toggleLesson = (lessonId: string) => {
    setExpandedLessons((prev) => ({
      ...prev,
      [lessonId]: !prev[lessonId],
    }))
  }

  // Format time to display as 1hr 30m
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}hr${minutes > 0 ? ` ${minutes}m` : ""}`
    } else {
      return `${minutes}m`
    }
  }

  // Get content type icon
  const getContentIcon = (type: string, isCompleted: boolean) => {
    if (isCompleted) {
      return <CheckCircle size={16} className="mr-2 flex-shrink-0 text-[#008268]" />
    }

    switch (type) {
      case "video":
        return <Video size={16} className="mr-2 flex-shrink-0" />
      case "text":
        return <BookOpen size={16} className="mr-2 flex-shrink-0" />
      case "pdf":
        return <FileText size={16} className="mr-2 flex-shrink-0" />
      default:
        return <BookOpen size={16} className="mr-2 flex-shrink-0" />
    }
  }

  // ตรวจสอบว่าเนื้อหาถูกทำเสร็จแล้วหรือไม่
  const isContentCompleted = (contentId: string) => {
    // ตรวจสอบจาก props ก่อน
    if (userProgress.completedContents?.includes(contentId)) {
      return true
    }

    // ถ้าไม่มีใน props ให้ตรวจสอบจาก localStorage
    if (typeof window !== "undefined" && userId) {
      try {
        const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")
        return completedContents.includes(contentId)
      } catch (e) {
        console.error("Error reading from localStorage:", e)
      }
    }

    return false
  }

  // ตรวจสอบว่าบทเรียนถูกทำเสร็จแล้วหรือไม่
  const isLessonCompleted = (lessonId: string) => {
    // ตรวจสอบจาก props ก่อน
    if (userProgress.completedLessons?.includes(lessonId)) {
      return true
    }

    // ถ้าไม่มีใน props ให้ตรวจสอบว่าทุกเนื้อหาในบทเรียนนี้ถูกทำเสร็จแล้วหรือไม่
    const lesson = courses.lesson.find((l: { id: string }) => l.id === lessonId)
    if (lesson) {
      return lesson.content.every((c: { id: string }) => isContentCompleted(c.id))
    }

    return false
  }

  // ตรวจสอบว่าทำทุกบทเรียนเสร็จแล้วหรือยัง (ป้องกัน undefined)
  const isAllLessonsCompleted = (): boolean => {
    // ถ้ามีข้อมูลจาก props ให้ใช้ข้อมูลจาก props
    if (userProgress.allLessonsCompleted !== undefined) {
      return userProgress.allLessonsCompleted
    }

    // ถ้าไม่มีข้อมูลจาก props ให้ตรวจสอบว่าทุกเนื้อหาในทุกบทเรียนถูกทำเสร็จแล้วหรือไม่
    if (typeof window !== "undefined" && userId) {
      try {
        const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")

        // ตรวจสอบว่าทุกเนื้อหาในทุกบทเรียนถูกทำเสร็จแล้วหรือไม่
        return courses.lesson.every((lesson: any) => {
          return lesson.content.every((content: any) => completedContents.includes(content.id))
        })
      } catch (e) {
        console.error("Error reading from localStorage:", e)
        return false
      }
    }

    return false
  }

  // ตรวจสอบว่าปัจจุบันกำลังอยู่ที่ข้อสอบท้ายบทหรือไม่
  const isOnFinalExam = () => {
    return select.selectId.lessonId === "final" && select.selectId.contentId === "final-exam"
  }

  const renderContent = () => {
    if (!menuOpen.opened) {
      return null
    }

    return (
      <div className="fixed inset-0 z-50 overflow-hidden">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={() => menuOpen.onMenuOpen(false)}
        />

        {/* Sidebar */}
        <div className="fixed inset-y-0 left-0 max-w-md w-full bg-white shadow-xl overflow-y-auto transform transition-transform duration-300 ease-in-out">
          {/* Header with close button */}
          <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-4 flex items-center justify-between">
            <a href="/courses" className="flex items-center text-gray-900 hover:text-[#008268] transition-colors">
              <ArrowLeft size={18} className="mr-2" />
              <span className="font-medium">กลับไปหลักสูตร</span>
            </a>
            <div
              className="w-8 h-8 flex items-center justify-center rounded-full bg-[#008268] text-white hover:bg-[#006e58] cursor-pointer transition-colors"
              onClick={() => menuOpen.onMenuOpen(false)}
            >
              <X size={18} />
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="space-y-6">
              {/* Course title and description */}
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{courses.name}</h2>
                <p className="mt-2 text-gray-600">{courses.description}</p>
              </div>

              {/* Teacher info */}
              <div className="bg-[#f8f9fa] p-4 rounded-lg flex items-center gap-4 border border-gray-100">
                <div className="w-12 h-12 rounded-full bg-[#e6f7f4] flex items-center justify-center text-[#008268] flex-shrink-0">
                  <User size={24} />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{courses.teacher.name}</div>
                  <div className="text-sm text-gray-600">{courses.teacher.description}</div>
                </div>
              </div>

              {/* Course content */}
              <div>
                <h3 className="text-lg font-bold mb-4 text-gray-800 flex items-center">
                  <BookOpen size={18} className="mr-2" />
                  เนื้อหาบทเรียน
                </h3>

                <div className="space-y-3">
                  {courses.lesson.map((lesson: lessonType, index: number) => (
                    <div
                      key={lesson.id}
                      className={`border rounded-lg overflow-hidden shadow-sm ${
                        isLessonCompleted(lesson.id) ? "border-[#008268] bg-[#E6F7F4]" : "border-gray-200"
                      }`}
                    >
                      {/* Lesson header */}
                      <div
                        className={`p-4 cursor-pointer transition-colors ${
                          lesson.id === select.selectId.lessonId
                            ? "bg-[#e6f7f4] border-b border-gray-200"
                            : isLessonCompleted(lesson.id)
                              ? "bg-[#E6F7F4] hover:bg-[#d1efe9]"
                              : "bg-white hover:bg-gray-50"
                        }`}
                        onClick={() => toggleLesson(lesson.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0 ${
                                isLessonCompleted(lesson.id) ? "bg-[#008268] text-white" : "bg-[#008268] text-white"
                              }`}
                            >
                              {isLessonCompleted(lesson.id) ? <CheckCircle size={16} /> : index + 1}
                            </div>
                            <span className="font-medium text-gray-800">{lesson.name}</span>
                          </div>
                          <div className="flex items-center">
                            <div className="flex items-center text-gray-500 mr-3">
                              <Clock size={14} className="mr-1" />
                              <span className="text-sm">{formatTime(lesson.time)}</span>
                            </div>
                            <ChevronDown
                              size={18}
                              className={`transition-transform text-[#008268] ${expandedLessons[lesson.id] ? "rotate-180" : ""}`}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Lesson content */}
                      {expandedLessons[lesson.id] && (
                        <div className="bg-white">
                          <div className="divide-y divide-gray-100">
                            {lesson.content.map((content: contentType, contentIndex: number) => (
                              <div
                                key={content.id}
                                onClick={() => {
                                  select.setSelectId({
                                    lessonId: lesson.id,
                                    contentId: content.id,
                                  })
                                  menuOpen.onMenuOpen(false)
                                }}
                                className={`flex items-center justify-between px-4 py-3 transition ${
                                  content.id === select.selectId.contentId && lesson.id === select.selectId.lessonId
                                    ? "bg-[#e6f7f4] text-[#008268]"
                                    : isContentCompleted(content.id)
                                      ? "bg-[#E6F7F4] text-gray-700 hover:bg-[#d1efe9]"
                                      : "text-gray-700 hover:bg-gray-50"
                                } hover:cursor-pointer`}
                              >
                                <div className="flex items-center ml-11">
                                  {getContentIcon(content.typecontent, isContentCompleted(content.id))}
                                  <span className="text-sm">
                                    {index + 1}.{contentIndex + 1} {content.name}
                                  </span>
                                </div>
                                <div className="flex items-center text-xs text-gray-500 flex-shrink-0">
                                  <Clock size={12} className="mr-1" />
                                  <span>{formatTime(content.time)}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* แสดงสอบท้ายบทแยกต่างหากเฉพาะเมื่อมีข้อสอบท้ายบท */}
              {hasFinalExam && (
                <div className="mt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText size={20} className="text-gray-700" />
                    <h3 className="text-lg font-medium text-gray-800">การประเมินผล</h3>
                  </div>
                  <div
                    className="p-3 rounded-lg mb-2 cursor-pointer transition-all bg-[#F0F2FA] border border-[#293D97] hover:bg-[#F0F2FA]"
                    onClick={() => {
                      const allCompleted = isAllLessonsCompleted()
                      console.log("All lessons completed check:", allCompleted)

                      if (allCompleted) {
                        select.setSelectId({
                          lessonId: "final",
                          contentId: "final-exam",
                        })
                        menuOpen.onMenuOpen(false)
                      } else {
                        // แสดงข้อความแจ้งเตือน
                        alert("คุณต้องทำควิซระหว่างบทให้ผ่านทุกข้อก่อน จึงจะสามารถทำสอบท้ายบทได้")
                      }
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {!isAllLessonsCompleted() && (
                          <Lock size={18} className={isOnFinalExam() ? "text-[#293D97]" : "text-gray-700"} />
                        )}
                        {isAllLessonsCompleted() && (
                          <CheckCircle size={18} className={isOnFinalExam() ? "text-[#293D97]" : "text-gray-700"} />
                        )}
                        <span className={`font-medium ${isOnFinalExam() ? "text-[#293D97]" : "text-gray-700"}`}>
                          สอบท้ายบท
                        </span>
                      </div>
                      <div
                        className={`flex items-center gap-1 ${isOnFinalExam() ? "text-[#293D97]" : "text-gray-700"}`}
                      >
                        <Clock size={16} />
                        <span>30 นาที</span>
                      </div>
                    </div>
                    {!isAllLessonsCompleted() && (
                      <p className={`text-sm mt-1 ${isOnFinalExam() ? "text-[#293D97]" : "text-gray-600"}`}>
                        ต้องทำควิซระหว่างบทให้ผ่านทุกข้อก่อน จึงจะสามารถทำสอบท้ายบทได้
                      </p>
                    )}
                    {isAllLessonsCompleted() && (
                      <p className={`text-sm mt-1 ${isOnFinalExam() ? "text-[#293D97]" : "text-gray-600"}`}>
                        คุณสามารถทำข้อสอบท้ายบทได้แล้ว คลิกเพื่อเริ่มทำข้อสอบ
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // ส่งข้อมูลคอร์สและบทเรียนไปให้ LearmingContent
  return (
    <div>
      {renderContent()}
      {/* Menu Button */}
      <div
        className={`fixed top-3 left-4 z-20 bg-white w-10 h-10 flex items-center justify-center rounded-full shadow-lg cursor-pointer transition-transform ${menuOpen.opened ? "rotate-180" : ""}`}
        onClick={() => menuOpen.onMenuOpen(!menuOpen.opened)}
      >
        {/* Animated Hamburger Icon */}
        <div className={`transition-transform ${menuOpen.opened ? "open" : ""}`}>
          <Menu size={20} className="text-gray-700" />
        </div>
      </div>
    </div>
  )
}
