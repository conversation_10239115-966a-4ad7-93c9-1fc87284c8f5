// const QuestionCard = (
//     { 
//         question, 
//         isQuestionOpen
//     }: { 
//         question: string
//         isQuestionOpen: boolean
//     }) => {

//     const handleCloseQuestion = () => {
//         // Close the question modal
//     };

//     const handAnswerWrong = () => {
//         // Close the question modal
//     }

//     return (
//         <div>
//             {isQuestionOpen && (
//                 <div className="modal modal-open">
//                     <div className="modal-box">
//                         <h3 className="font-bold text-lg">Quiz Time!</h3>
//                         <p className="py-4">What is the correct answer?</p>
//                         <div className="modal-action">
//                             <button className="btn btn-primary" onClick={handleCloseQuestion}>
//                                 Answer correct
//                             </button>
//                             <button className="btn" onClick={handAnswerWrong}>
//                                 Answer wrong
//                             </button>
//                         </div>
//                     </div>
//                 </div>
//             )}
//         </div>
//     );
// };
