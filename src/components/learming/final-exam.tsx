"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { <PERSON>, CheckCircle, AlertTriangle, ArrowLeft, ChevronLeft, ChevronRight, Info } from "lucide-react"
import { useRouter } from "next/navigation"

interface Choice {
  id: string
  content: string
  type?: string // เปลี่ยนจาก "text" | "image" เป็น string ทั่วไป และเป็น optional
  isCorrect: boolean
  imageUrl?: string
}

interface Question {
  id: string
  title: string
  type?: string // เปลี่ยนจาก "text" | "image" | "text_and_image" | "video" เป็น string ทั่วไป และเป็น optional
  content: string
  imageUrl?: string
  choices: Choice[]
}

interface FinalExamProps {
  examData: {
    id: string
    name: string
    description?: string
    questions: Question[]
    passingScore: number
    timeLimit: number
  }
  onComplete: (passed: boolean, score: number) => void
}

const FinalExam: React.FC<FinalExamProps> = ({ examData, onComplete }) => {
  const router = useRouter()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string[]>>({})
  const [timeLeft, setTimeLeft] = useState(examData.timeLimit * 60) // เวลาในหน่วยวินาที
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [examResult, setExamResult] = useState<{ passed: boolean; score: number; correctAnswers: number } | null>(null)
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false)
  const [showAllQuestions, setShowAllQuestions] = useState(false) // เพิ่มสถานะสำหรับการแสดงทุกข้อพร้อมกัน
  const [answeredQuestions, setAnsweredQuestions] = useState<string[]>([]) // เก็บ ID ของคำถามที่ตอบแล้ว
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const [showReview, setShowReview] = useState(false) // สถานะสำหรับการแสดงทบทวนคำตอบ

  // ตั้งค่าเริ่มต้นสำหรับคำตอบที่เลือก
  useEffect(() => {
    const initialAnswers: Record<string, string[]> = {}
    examData.questions.forEach((q) => {
      initialAnswers[q.id] = []
    })
    setSelectedAnswers(initialAnswers)
  }, [examData.questions])

  // ตั้งเวลานับถอยหลัง
  useEffect(() => {
    if (timeLeft <= 0 || isSubmitted) return

    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          if (timerRef.current) clearInterval(timerRef.current)
          handleSubmitExam() // หมดเวลาให้ส่งข้อสอบอัตโนมัติ
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => {
      if (timerRef.current) clearInterval(timerRef.current)
    }
  }, [timeLeft, isSubmitted])

  // ฟังก์ชันเลือกคำตอบ
  const handleSelectAnswer = (questionId: string, choiceId: string) => {
    if (isSubmitted) return // ไม่ให้เปลี่ยนคำตอบหลังจากส่งข้อสอบแล้ว

    setSelectedAnswers((prev) => {
      // ถ้ายังไม่มีคำตอบสำหรับคำถามนี้ ให้สร้างอาร์เรย์ใหม่
      if (!prev[questionId]) {
        return {
          ...prev,
          [questionId]: [choiceId],
        }
      }

      // ถ้ามีคำตอบอยู่แล้ว ให้ตรวจสอบว่ามีคำตอบนี้หรือไม่
      const existingAnswers = prev[questionId]
      if (existingAnswers.includes(choiceId)) {
        // ถ้ามีคำตอบนี้อยู่แล้ว ให้ลบออก
        return {
          ...prev,
          [questionId]: existingAnswers.filter((id) => id !== choiceId),
        }
      } else {
        // ถ้ายังไม่มีคำตอบนี้ ให้เพิ่มเข้าไป
        return {
          ...prev,
          [questionId]: [...existingAnswers, choiceId],
        }
      }
    })

    // เพิ่มคำถามนี้เข้าไปในรายการคำถามที่ตอบแล้ว
    if (!answeredQuestions.includes(questionId)) {
      setAnsweredQuestions((prev) => [...prev, questionId])
    }
  }

  // ฟังก์ชันไปยังคำถามถัดไป
  const goToNextQuestion = () => {
    if (currentQuestion < examData.questions.length - 1) {
      setCurrentQuestion((prev) => prev + 1)
    }
  }

  // ฟังก์ชันไปยังคำถามก่อนหน้า
  const goToPrevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion((prev) => prev - 1)
    }
  }

  // ฟังก์ชันตรวจสอบคำตอบและคำนวณคะแนน
  const calculateScore = () => {
    let correctCount = 0

    examData.questions.forEach((question) => {
      const userAnswers = selectedAnswers[question.id] || []
      const correctAnswers = question.choices.filter((choice) => choice.isCorrect).map((choice) => choice.id)

      // ตรวจสอบว่าคำตอบถูกต้องหรือไม่ (ต้องเลือกครบทุกข้อที่ถูกและไม่เลือกข้อที่ผิด)
      const isCorrect =
        userAnswers.length === correctAnswers.length && userAnswers.every((id) => correctAnswers.includes(id))

      if (isCorrect) {
        correctCount++
      }
    })

    const score = Math.round((correctCount / examData.questions.length) * 100)
    const passed = score >= examData.passingScore

    return { passed, score, correctAnswers: correctCount }
  }

  // ฟังก์ชันส่งข้อสอบ
  const handleSubmitExam = () => {
    if (isSubmitted) return

    // หยุดการนับเวลา
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }

    const result = calculateScore()
    setExamResult(result)
    setIsSubmitted(true)

    // แจ้งผลการสอบไปยัง parent component
    if (onComplete) {
      onComplete(result.passed, result.score)
    }
  }

  // ฟอร์แมตเวลาเป็น mm:ss
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  // ตรวจสอบว่าตอบคำถามครบทุกข้อหรือไม่
  const allQuestionsAnswered = examData.questions.every(
    (q) => selectedAnswers[q.id] && selectedAnswers[q.id].length > 0,
  )

  // ฟังก์ชันสลับโหมดการแสดงผล (แสดงทีละข้อ หรือ แสดงทุกข้อพร้อมกัน)
  const toggleDisplayMode = () => {
    setShowAllQuestions((prev) => !prev)
  }

  // ตรวจสอบว่าคำถามมีคำตอบที่ถูกต้องมากกว่า 1 ข้อหรือไม่
  const hasMultipleCorrectAnswers = (questionId: string): boolean => {
    const question = examData.questions.find((q) => q.id === questionId)
    if (!question) return false

    const correctAnswersCount = question.choices.filter((c) => c.isCorrect).length
    return correctAnswersCount > 1
  }

  // ฟังก์ชันเริ่มทำข้อสอบใหม่
  const restartExam = () => {
    setIsSubmitted(false)
    setExamResult(null)
    setSelectedAnswers({})
    setCurrentQuestion(0)
    setTimeLeft(examData.timeLimit * 60)
    setAnsweredQuestions([])
    setShowReview(false)
  }

  // ถ้าส่งข้อสอบแล้ว ให้แสดงผลลัพธ์
  if (isSubmitted && examResult && !showReview) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
          <div className={`p-6 text-center ${examResult.passed ? "bg-green-50" : "bg-red-50"}`}>
            <div
              className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center ${
                examResult.passed ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"
              }`}
            >
              {examResult.passed ? <CheckCircle size={40} /> : <AlertTriangle size={40} />}
            </div>

            <h2 className={`text-2xl font-bold mt-4 ${examResult.passed ? "text-green-600" : "text-red-600"}`}>
              {examResult.passed ? "สอบผ่าน!" : "สอบไม่ผ่าน"}
            </h2>

            <p className="text-gray-600 mt-2">
              คุณได้คะแนน {examResult.score}% ({examResult.correctAnswers} จาก {examData.questions.length} ข้อ)
            </p>

            <p className="text-gray-500 mt-1">เกณฑ์ผ่าน: {examData.passingScore}%</p>
          </div>

          <div className="p-6 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row sm:justify-between gap-3">
              <button
                onClick={() => router.push("/courses")}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center justify-center"
              >
                <ArrowLeft size={16} className="mr-2" />
                กลับไปหน้าคอร์ส
              </button>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowReview(true)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex-1 sm:flex-none"
                >
                  ทบทวนคำตอบ
                </button>

                {!examResult.passed && (
                  <button
                    onClick={restartExam}
                    className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] flex-1 sm:flex-none"
                  >
                    ทำข้อสอบใหม่
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // แสดงหน้าทบทวนคำตอบ
  if (showReview) {
    const currentQuestionData = examData.questions[currentQuestion]
    const userAnswers = selectedAnswers[currentQuestionData.id] || []
    const correctAnswers = currentQuestionData.choices.filter((choice) => choice.isCorrect).map((choice) => choice.id)

    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-md mb-6 p-4">
            <div className="flex justify-between items-center">
              <h1 className="text-xl font-bold">ทบทวนคำตอบ: {examData.name}</h1>
              <button
                onClick={() => setShowReview(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                กลับไปผลสอบ
              </button>
            </div>
          </div>

          {/* Question navigation */}
          <div className="bg-white rounded-xl shadow-md mb-6 p-4">
            <div className="flex flex-wrap gap-2">
              {examData.questions.map((q, index) => {
                const qUserAnswers = selectedAnswers[q.id] || []
                const qCorrectAnswers = q.choices.filter((c) => c.isCorrect).map((c) => c.id)
                const isCorrect =
                  qUserAnswers.length === qCorrectAnswers.length &&
                  qUserAnswers.every((id) => qCorrectAnswers.includes(id))

                return (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestion(index)}
                    className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      currentQuestion === index
                        ? "bg-[#008268] text-white"
                        : isCorrect
                          ? "bg-green-100 text-green-700 border border-green-500"
                          : "bg-red-100 text-red-700 border border-red-500"
                    }`}
                  >
                    {index + 1}
                  </button>
                )
              })}
            </div>
          </div>

          {/* Question and answers */}
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">
                คำถามที่ {currentQuestion + 1} จาก {examData.questions.length}
              </h2>
              <div className="flex gap-2">
                <button
                  onClick={goToPrevQuestion}
                  disabled={currentQuestion === 0}
                  className="p-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  onClick={goToNextQuestion}
                  disabled={currentQuestion === examData.questions.length - 1}
                  className="p-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight size={20} />
                </button>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-xl font-medium text-gray-800 mb-2 flex items-center">
                <span className="bg-[#008268] text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">
                  {currentQuestion + 1}
                </span>
                {currentQuestionData.title}
              </h3>
              {currentQuestionData.content && <p className="text-gray-600 mb-4 ml-11">{currentQuestionData.content}</p>}
              {currentQuestionData.imageUrl && (
                <div className="mb-4 ml-11">
                  <img
                    src={currentQuestionData.imageUrl || "/placeholder.svg"}
                    alt="Question"
                    className="max-h-64 object-contain rounded-lg"
                  />
                </div>
              )}
            </div>

            {/* Choices with correct/incorrect indicators */}
            <div className="space-y-3 mb-8 ml-11">
              {currentQuestionData.choices.map((choice, choiceIndex) => {
                const isSelected = userAnswers.includes(choice.id)
                const isCorrect = choice.isCorrect

                let borderColor = "border-gray-200"
                let bgColor = "bg-white"

                if (isSelected && isCorrect) {
                  borderColor = "border-green-500"
                  bgColor = "bg-green-50"
                } else if (isSelected && !isCorrect) {
                  borderColor = "border-red-500"
                  bgColor = "bg-red-50"
                } else if (!isSelected && isCorrect) {
                  borderColor = "border-green-500"
                  bgColor = "bg-green-50"
                }

                return (
                  <div key={choice.id} className={`p-4 border rounded-lg ${borderColor} ${bgColor}`}>
                    <div className="flex items-center">
                      <div
                        className={`w-6 h-6 flex-shrink-0 rounded-md border flex items-center justify-center mr-3 ${
                          isSelected
                            ? isCorrect
                              ? "border-green-500 bg-green-500 text-white"
                              : "border-red-500 bg-red-500 text-white"
                            : isCorrect
                              ? "border-green-500 bg-green-500 text-white"
                              : "border-gray-300"
                        }`}
                      >
                        {isSelected || isCorrect ? (
                          isCorrect ? (
                            <CheckCircle size={16} />
                          ) : (
                            <span className="text-white">✗</span>
                          )
                        ) : null}
                      </div>
                      <div className="flex items-center">
                        <span className="mr-2 text-gray-600 font-medium">{String.fromCharCode(65 + choiceIndex)}.</span>
                        <div className="flex-1">
                          {choice.type === "image" || choice.imageUrl ? (
                            <div className="flex flex-col">
                              <img
                                src={choice.imageUrl || "/placeholder.svg"}
                                alt={choice.content}
                                className="h-20 object-contain mb-2"
                              />
                              {choice.content && <span className="text-sm">{choice.content}</span>}
                            </div>
                          ) : (
                            <span>{choice.content}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // แสดงทุกข้อพร้อมกัน
  if (showAllQuestions) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-md mb-6 p-4">
            <div className="flex justify-between items-center">
              <h1 className="text-xl font-bold">{examData.name}</h1>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <Clock size={18} className="mr-2" />
                  <span className={`font-mono ${timeLeft < 60 ? "text-red-500 animate-pulse" : ""}`}>
                    {formatTime(timeLeft)}
                  </span>
                </div>
                <button
                  onClick={toggleDisplayMode}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200"
                >
                  แสดงทีละข้อ
                </button>
              </div>
            </div>
            {examData.description && <p className="text-gray-600 mt-2">{examData.description}</p>}
          </div>

          {/* คำแนะนำ */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
            <div className="flex items-start">
              <Info size={20} className="text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-blue-700 text-sm font-medium">คำแนะนำในการทำข้อสอบ:</p>
                <p className="text-blue-700 text-sm mt-1">
                  กรุณาเลือกคำตอบที่ถูกต้องทั้งหมด (อาจมีมากกว่า 1 ข้อ) สำหรับแต่ละคำถาม เมื่อทำเสร็จแล้วให้กดปุ่ม "ส่งคำตอบ" ด้านล่าง
                </p>
              </div>
            </div>
          </div>

          {/* Questions */}
          <div className="space-y-8 mb-8">
            {examData.questions.map((question, index) => {
              const multipleCorrect = hasMultipleCorrectAnswers(question.id)

              return (
                <div key={question.id} className="bg-white rounded-xl shadow-md p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium flex items-start">
                      <span className="bg-[#008268] text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                        {index + 1}
                      </span>
                      <span>{question.title}</span>
                    </h3>
                    {question.content && <p className="text-gray-600 mt-2 ml-11">{question.content}</p>}

                    {multipleCorrect && (
                      <div className="ml-11 mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800 flex items-center">
                        <AlertTriangle size={16} className="mr-2 flex-shrink-0" />
                        <span>คำถามนี้มีคำตอบที่ถูกต้องมากกว่า 1 ข้อ กรุณาเลือกทุกข้อที่ถูกต้อง</span>
                      </div>
                    )}

                    {question.imageUrl && (
                      <div className="mt-4 ml-11">
                        <img
                          src={question.imageUrl || "/placeholder.svg"}
                          alt="Question"
                          className="max-h-64 object-contain rounded-lg"
                        />
                      </div>
                    )}
                  </div>

                  {/* Choices */}
                  <div className="space-y-3 ml-11">
                    {question.choices.map((choice, choiceIndex) => (
                      <div
                        key={choice.id}
                        onClick={() => handleSelectAnswer(question.id, choice.id)}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedAnswers[question.id]?.includes(choice.id)
                            ? "border-[#008268] bg-[#e6f7f4]"
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        <div className="flex items-center">
                          <div
                            className={`w-6 h-6 flex-shrink-0 rounded-md border ${
                              selectedAnswers[question.id]?.includes(choice.id)
                                ? "border-[#008268] bg-[#008268]"
                                : "border-gray-300"
                            } flex items-center justify-center mr-3`}
                          >
                            {selectedAnswers[question.id]?.includes(choice.id) && (
                              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </div>
                          <div className="flex items-center">
                            <span className="mr-2 text-gray-600 font-medium">
                              {String.fromCharCode(65 + choiceIndex)}.
                            </span>
                            <div className="flex-1">
                              {choice.type === "image" || choice.imageUrl ? (
                                <div className="flex flex-col">
                                  <img
                                    src={choice.imageUrl || "/placeholder.svg"}
                                    alt={choice.content}
                                    className="h-20 object-contain mb-2"
                                  />
                                  {choice.content && <span className="text-sm">{choice.content}</span>}
                                </div>
                              ) : (
                                <span>{choice.content}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Submit button */}
          <div className="flex justify-between bg-white rounded-xl shadow-md p-4 sticky bottom-4">
            <button
              onClick={toggleDisplayMode}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              แสดงทีละข้อ
            </button>
            <button
              onClick={() => setShowConfirmSubmit(true)}
              disabled={!allQuestionsAnswered}
              className={`px-4 py-2 rounded-md ${
                allQuestionsAnswered
                  ? "bg-[#008268] text-white hover:bg-[#006e58]"
                  : "bg-gray-200 text-gray-500 cursor-not-allowed"
              }`}
            >
              ส่งคำตอบ
            </button>
          </div>
        </div>

        {/* Confirm submit modal */}
        {showConfirmSubmit && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">ยืนยันการส่งคำตอบ</h3>
              <p className="text-gray-600 mb-6">
                คุณแน่ใจหรือไม่ว่าต้องการส่งคำตอบ? คุณได้ตอบคำถามครบ {answeredQuestions.length} จาก {examData.questions.length}{" "}
                ข้อ
              </p>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowConfirmSubmit(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  ยกเลิก
                </button>
                <button
                  onClick={() => {
                    setShowConfirmSubmit(false)
                    handleSubmitExam()
                  }}
                  className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
                >
                  ยืนยันการส่ง
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  // แสดงทีละข้อ (โหมดเดิม)
  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        {/* Header */}
        <div className="bg-[#008268] text-white p-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-bold">{examData.name}</h1>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Clock size={18} className="mr-2" />
                <span className={`font-mono ${timeLeft < 60 ? "text-red-300 animate-pulse" : ""}`}>
                  {formatTime(timeLeft)}
                </span>
              </div>
              <button
                onClick={toggleDisplayMode}
                className="px-3 py-1 bg-white bg-opacity-20 text-white rounded-md text-sm hover:bg-opacity-30"
              >
                แสดงทุกข้อ
              </button>
            </div>
          </div>

          {/* Progress bar */}
          <div className="w-full bg-white bg-opacity-30 h-2 mt-3 rounded-full overflow-hidden">
            <div
              className="bg-white h-full rounded-full"
              style={{ width: `${((currentQuestion + 1) / examData.questions.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Question */}
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">
              คำถามที่ {currentQuestion + 1} จาก {examData.questions.length}
            </h2>
            <span className="text-sm text-gray-500">
              ตอบแล้ว {answeredQuestions.length} จาก {examData.questions.length} ข้อ
            </span>
          </div>

          <div className="mb-6">
            <h3 className="text-xl font-medium text-gray-800 mb-2 flex items-center">
              <span className="bg-[#008268] text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">
                {currentQuestion + 1}
              </span>
              {examData.questions[currentQuestion].title}
            </h3>
            {examData.questions[currentQuestion].content && (
              <p className="text-gray-600 mb-4 ml-11">{examData.questions[currentQuestion].content}</p>
            )}

            {hasMultipleCorrectAnswers(examData.questions[currentQuestion].id) && (
              <div className="ml-11 mb-4 p-2 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800 flex items-center">
                <AlertTriangle size={16} className="mr-2 flex-shrink-0" />
                <span>คำถามนี้มีคำตอบที่ถูกต้องมากกว่า 1 ข้อ กรุณาเลือกทุกข้อที่ถูกต้อง</span>
              </div>
            )}

            {examData.questions[currentQuestion].imageUrl && (
              <div className="mb-4 ml-11">
                <img
                  src={examData.questions[currentQuestion].imageUrl || "/placeholder.svg"}
                  alt="Question"
                  className="max-h-64 object-contain rounded-lg"
                />
              </div>
            )}
          </div>

          {/* Choices */}
          <div className="space-y-3 mb-8 ml-11">
            {examData.questions[currentQuestion].choices.map((choice, choiceIndex) => (
              <div
                key={choice.id}
                onClick={() => handleSelectAnswer(examData.questions[currentQuestion].id, choice.id)}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedAnswers[examData.questions[currentQuestion].id]?.includes(choice.id)
                    ? "border-[#008268] bg-[#e6f7f4]"
                    : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                }`}
              >
                <div className="flex items-center">
                  <div
                    className={`w-6 h-6 flex-shrink-0 rounded-md border ${
                      selectedAnswers[examData.questions[currentQuestion].id]?.includes(choice.id)
                        ? "border-[#008268] bg-[#008268]"
                        : "border-gray-300"
                    } flex items-center justify-center mr-3`}
                  >
                    {selectedAnswers[examData.questions[currentQuestion].id]?.includes(choice.id) && (
                      <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    )}
                  </div>
                  <div className="flex items-center">
                    <span className="mr-2 text-gray-600 font-medium">{String.fromCharCode(65 + choiceIndex)}.</span>
                    <div className="flex-1">
                      {choice.type === "image" || choice.imageUrl ? (
                        <div className="flex flex-col">
                          <img
                            src={choice.imageUrl || "/placeholder.svg"}
                            alt={choice.content}
                            className="h-20 object-contain mb-2"
                          />
                          {choice.content && <span className="text-sm">{choice.content}</span>}
                        </div>
                      ) : (
                        <span>{choice.content}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-between">
            <button
              onClick={goToPrevQuestion}
              disabled={currentQuestion === 0}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <ChevronLeft size={18} className="mr-1" />
              ข้อก่อนหน้า
            </button>

            {currentQuestion < examData.questions.length - 1 ? (
              <button
                onClick={goToNextQuestion}
                className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] flex items-center"
              >
                ข้อถัดไป
                <ChevronRight size={18} className="ml-1" />
              </button>
            ) : (
              <button
                onClick={() => setShowConfirmSubmit(true)}
                disabled={!allQuestionsAnswered}
                className={`px-4 py-2 rounded-md ${
                  allQuestionsAnswered
                    ? "bg-[#008268] text-white hover:bg-[#006e58]"
                    : "bg-gray-200 text-gray-500 cursor-not-allowed"
                }`}
              >
                ส่งคำตอบ
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Question navigation */}
      <div className="max-w-3xl mx-auto mt-4 bg-white rounded-xl shadow-sm p-4">
        <div className="flex flex-wrap gap-2">
          {examData.questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentQuestion(index)}
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                currentQuestion === index
                  ? "bg-[#008268] text-white"
                  : answeredQuestions.includes(examData.questions[index].id)
                    ? "bg-[#e6f7f4] text-[#008268] border border-[#008268]"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>

      {/* Fixed submit button */}
      {currentQuestion !== examData.questions.length - 1 && (
        <div className="fixed bottom-6 right-6">
          <button
            onClick={() => setShowConfirmSubmit(true)}
            disabled={!allQuestionsAnswered}
            className={`px-6 py-3 rounded-md shadow-lg flex items-center ${
              allQuestionsAnswered
                ? "bg-[#008268] text-white hover:bg-[#006e58]"
                : "bg-gray-200 text-gray-500 cursor-not-allowed"
            }`}
          >
            <span>ส่งคำตอบ</span>
            <span className="ml-2 bg-white text-[#008268] px-2 py-0.5 rounded-full text-sm">
              {answeredQuestions.length}/{examData.questions.length}
            </span>
          </button>
        </div>
      )}

      {/* Confirm submit modal */}
      {showConfirmSubmit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">ยืนยันการส่งคำตอบ</h3>
            <p className="text-gray-600 mb-6">
              คุณแน่ใจหรือไม่ว่าต้องการส่งคำตอบ? คุณได้ตอบคำถามครบ {answeredQuestions.length} จาก {examData.questions.length} ข้อ
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmSubmit(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                ยกเลิก
              </button>
              <button
                onClick={() => {
                  setShowConfirmSubmit(false)
                  handleSubmitExam()
                }}
                className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
              >
                ยืนยันการส่ง
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FinalExam
