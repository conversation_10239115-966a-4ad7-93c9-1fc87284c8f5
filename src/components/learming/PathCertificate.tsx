"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { Download, X } from "lucide-react"

interface PathCertificateProps {
    userName: string
    pathName: string
    completionDate: Date
    isOpen: boolean
    onClose: () => void
}

const PathCertificate: React.FC<PathCertificateProps> = ({ userName, pathName, completionDate, isOpen, onClose }) => {
    const certificateRef = useRef<HTMLDivElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const [isGenerating, setIsGenerating] = useState(false)
    const [fontLoaded, setFontLoaded] = useState(false)
    const [imageLoaded, setImageLoaded] = useState(false)
    const [logoLoaded, setLogoLoaded] = useState(false)

    const formattedDate = completionDate.toLocaleDateString("th-TH", {
        year: "numeric",
        month: "long",
        day: "numeric",
    })

    const isReady = fontLoaded && imageLoaded && logoLoaded

    useEffect(() => {
        const loadResources = async () => {
            if (!isOpen) return

            try {
                // Load font with better error handling
                try {
                    const font = new FontFace("NotoSansThai", "url(/fonts/NotoSansThai-Regular.ttf)")
                    await font.load()
                    document.fonts.add(font)
                    console.log("✅ Font loaded successfully")
                } catch (fontError) {
                    console.warn("⚠️ Font loading failed, using fallback:", fontError)
                }
                setFontLoaded(true)

                // Preload background image - remove crossOrigin for local files
                const img = new Image()
                img.onload = () => {
                    console.log("✅ Path background image loaded")
                    setImageLoaded(true)
                }
                img.onerror = (error) => {
                    console.warn("⚠️ Failed to load path background image:", error)
                    setImageLoaded(true) // Set to true to continue
                }
                img.src = "/e-med/img/PathCertificate.png"

                // Preload logo image - remove crossOrigin for local files
                const logoImg = new Image()
                logoImg.onload = () => {
                    console.log("✅ Logo image loaded")
                    setLogoLoaded(true)
                }
                logoImg.onerror = (error) => {
                    console.warn("⚠️ Failed to load logo image:", error)
                    setLogoLoaded(true) // Set to true to continue
                }
                logoImg.src = "/e-med/img/logo-1.png"
            } catch (error) {
                console.warn("⚠️ Failed to load resources:", error)
                // Set all to true to allow component to continue working
                setFontLoaded(true)
                setImageLoaded(true)
                setLogoLoaded(true)
            }
        }

        loadResources()
    }, [isOpen])

    const generateCanvas = async (): Promise<HTMLCanvasElement> => {
        return new Promise((resolve, reject) => {
            try {
                const canvas = document.createElement("canvas")
                const ctx = canvas.getContext("2d")
                if (!ctx) throw new Error("ไม่สามารถสร้าง canvas ได้")

                // ขนาดสูงสำหรับคุณภาพการพิมพ์
                canvas.width = 3508 // A4 300 DPI width
                canvas.height = 2480 // A4 300 DPI height

                const img = new Image()
                img.onload = () => {
                    // วาดพื้นหลัง
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

                    const logoImg = new Image()
                    logoImg.onload = () => {
                        const scale = canvas.width / 1200 // อัตราส่วนจาก 1200px เป็น 3508px
                        ctx.drawImage(logoImg, 90 * scale, 100 * scale, 250 * scale, 80 * scale)

                        const fontFamily = "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif"

                        ctx.fillStyle = "#424242"
                        ctx.font = `${50 * scale}px ${fontFamily}`
                        ctx.textAlign = "left"
                        ctx.fillText("ประกาศนียบัตร", 100 * scale, 240 * scale)

                        ctx.fillStyle = "#424242"
                        ctx.font = `${30 * scale}px ${fontFamily}`
                        ctx.fillText("ขอมอบประกาศนียบฉบับนี้เพื่อแสดงว่า", 100 * scale, 300 * scale)

                        ctx.fillStyle = "#008268"
                        ctx.font = `bold ${45 * scale}px ${fontFamily}`
                        ctx.fillText(userName, 100 * scale, 410 * scale)

                        ctx.fillStyle = "#424242"
                        ctx.font = `${30 * scale}px ${fontFamily}`
                        ctx.fillText("ได้สำเร็จเส้นทางการเรียนรู้ผ่าน Synphaet E-MED Learning", 100 * scale, 500 * scale)

                        ctx.fillStyle = "#424242"
                        let currentY = 560 * scale
                        let currentX = 100 * scale

                        ctx.font = `${30 * scale}px ${fontFamily}`
                        ctx.fillText("ในเส้นทาง ", currentX, currentY)

                        const prefixWidth = ctx.measureText("ในเส้นทาง ").width
                        currentX += prefixWidth

                        ctx.font = `bold ${30 * scale}px ${fontFamily}`
                        const pathText = `"${pathName}"`

                        // ตรวจสอบว่าข้อความยาวเกินไปหรือไม่
                        const maxWidth = 660 * scale - prefixWidth
                        const pathTextWidth = ctx.measureText(pathText).width

                        if (pathTextWidth > maxWidth) {
                            // แบ่งข้อความหลายบรรทัด
                            const words = pathName.split(" ")
                            let line = ""

                            for (let n = 0; n < words.length; n++) {
                                const testLine = line + words[n] + " "
                                const testText = `"${line.trim()}"`
                                const metrics = ctx.measureText(testText)
                                const testWidth = metrics.width

                                if (testWidth > maxWidth && n > 0) {
                                    ctx.fillText(`"${line.trim()}"`, currentX, currentY)
                                    line = words[n] + " "
                                    currentY += 40 * scale
                                    currentX = 100 * scale // รีเซ็ตตำแหน่ง X สำหรับบรรทัดใหม่
                                } else {
                                    line = testLine
                                }
                            }
                            if (line.trim()) {
                                ctx.fillText(`"${line.trim()}"`, currentX, currentY)
                            }
                        } else {
                            ctx.fillText(pathText, currentX, currentY)
                        }


                        ctx.fillStyle = "#424242"
                        ctx.font = `${24 * scale}px ${fontFamily}`
                        ctx.textAlign = "right"
                        ctx.fillText(`เมื่อวันที่ ${formattedDate}`, 1050 * scale, 760 * scale)


                        ctx.fillStyle = "#424242"
                        ctx.font = `${20 * scale}px ${fontFamily}`
                        ctx.textAlign = "left"
                        ctx.fillText("ดร.มาเซอลีน เกียรติ์ชัยสิริพร", 125 * scale, 730 * scale)


                        ctx.fillStyle = "#666666"
                        ctx.font = `${20 * scale}px ${fontFamily}`
                        ctx.fillText("(ผู้อำนวยการโรงพยาบาลสินแพทย์)", 100 * scale, 760 * scale)

                        resolve(canvas)
                    }

                    logoImg.onerror = () => {
                        // ถ้าโลโก้โหลดไม่ได้ ให้ดำเนินการต่อโดยไม่มีโลโก้
                        const fontFamily = "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif"
                        const scale = canvas.width / 1200

                        ctx.fillStyle = "#424242"
                        ctx.font = `${50 * scale}px ${fontFamily}`
                        ctx.textAlign = "left"
                        ctx.fillText("ประกาศนียบัตร", 100 * scale, 240 * scale)

                        ctx.fillStyle = "#424242"
                        ctx.font = `${30 * scale}px ${fontFamily}`
                        ctx.fillText("ขอมอบประกาศนียบฉบับนี้เพื่อแสดงว่า", 100 * scale, 300 * scale)

                        ctx.fillStyle = "#008268"
                        ctx.font = `bold ${60 * scale}px ${fontFamily}`
                        ctx.fillText(userName, 100 * scale, 380 * scale)

                        ctx.fillStyle = "#424242"
                        ctx.font = `${30 * scale}px ${fontFamily}`
                        ctx.fillText("ได้สำเร็จเส้นทางการเรียนรู้ผ่าน Synphaet E-MED Learning", 100 * scale, 460 * scale)


                        ctx.fillStyle = "#424242"
                        let currentY = 510 * scale
                        let currentX = 100 * scale

                        ctx.font = `${30 * scale}px ${fontFamily}`
                        ctx.fillText("ในเส้นทาง ", currentX, currentY)

                        const prefixWidth = ctx.measureText("ในเส้นทาง ").width
                        currentX += prefixWidth

                        ctx.font = `bold ${30 * scale}px ${fontFamily}`
                        const pathText = `"${pathName}"`

                        const maxWidth = 660 * scale - prefixWidth
                        const pathTextWidth = ctx.measureText(pathText).width

                        if (pathTextWidth > maxWidth) {
                            const words = pathName.split(" ")
                            let line = ""

                            for (let n = 0; n < words.length; n++) {
                                const testLine = line + words[n] + " "
                                const testText = `"${line.trim()}"`
                                const metrics = ctx.measureText(testText)
                                const testWidth = metrics.width

                                if (testWidth > maxWidth && n > 0) {
                                    ctx.fillText(`"${line.trim()}"`, currentX, currentY)
                                    line = words[n] + " "
                                    currentY += 40 * scale
                                    currentX = 100 * scale
                                } else {
                                    line = testLine
                                }
                            }
                            if (line.trim()) {
                                ctx.fillText(`"${line.trim()}"`, currentX, currentY)
                            }
                        } else {
                            ctx.fillText(pathText, currentX, currentY)
                        }

                        ctx.fillStyle = "#424242"
                        ctx.font = `${24 * scale}px ${fontFamily}`
                        ctx.textAlign = "right"
                        ctx.fillText(`เมื่อวันที่ ${formattedDate}`, 1050 * scale, 723 * scale)

                        ctx.fillStyle = "#424242"
                        ctx.font = `${20 * scale}px ${fontFamily}`
                        ctx.textAlign = "left"
                        ctx.fillText("ดร.มาเซอลีน เกียรติ์ชัยสิริพร", 125 * scale, 710 * scale)

                        ctx.fillStyle = "#666666"
                        ctx.font = `${20 * scale}px ${fontFamily}`
                        ctx.fillText("(ผู้อำนวยการโรงพยาบาลสินแพทย์)", 100 * scale, 740 * scale)

                        resolve(canvas)
                    }

                    logoImg.src = "/e-med/img/logo-1.png"
                }

                img.onerror = () => {
                    reject(new Error("ไม่สามารถโหลดรูปพื้นหลังเส้นทางการเรียนได้"))
                }

                img.src = "/e-med/img/PathCertificate.png"
            } catch (error) {
                reject(error)
            }
        })
    }

    const downloadCertificate = async () => {
        if (!isReady) return

        setIsGenerating(true)

        try {
            const canvas = await generateCanvas()

            // ดาวน์โหลด PNG
            const link = document.createElement("a")
            link.download = `path-certificate-${userName.replace(/\s+/g, "-")}.png`
            link.href = canvas.toDataURL("image/png", 1.0)
            link.click()

            console.log("✅ Path Certificate PNG downloaded successfully")
        } catch (error) {
            console.error("❌ Error generating path certificate:", error)
            alert("เกิดข้อผิดพลาดในการสร้างใบประกาศนียบัตรเส้นทางการเรียน")
        }

        setIsGenerating(false)
    }

    const downloadPDF = async () => {
        if (!isReady) return

        setIsGenerating(true)

        try {
            const { jsPDF } = await import("jspdf")
            const canvas = await generateCanvas()

            const imgData = canvas.toDataURL("image/png", 1.0)


            const pdf = new jsPDF({
                orientation: "landscape",
                unit: "mm",
                format: "a4",
            })

            const pdfWidth = pdf.internal.pageSize.getWidth()
            const pdfHeight = pdf.internal.pageSize.getHeight()


            const imgWidth = canvas.width
            const imgHeight = canvas.height
            const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight)

            const finalWidth = imgWidth * ratio
            const finalHeight = imgHeight * ratio


            const x = (pdfWidth - finalWidth) / 2
            const y = (pdfHeight - finalHeight) / 2

            pdf.addImage(imgData, "PNG", x, y, finalWidth, finalHeight, undefined, "FAST")
            pdf.save(`path-certificate-${userName.replace(/\s+/g, "-")}.pdf`)

            console.log("✅ Path PDF downloaded successfully")
        } catch (error) {
            console.error("❌ Error generating path PDF:", error)
            if (error instanceof Error) {
                alert("เกิดข้อผิดพลาดในการสร้าง PDF: " + error.message)
            } else {
                alert("เกิดข้อผิดพลาดในการสร้าง PDF")
            }
        }

        setIsGenerating(false)
    }

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 backdrop-blur-[1.5px] bg-black bg-opacity-40 flex items-center justify-center z-50  p-2 sm:p-4 ">
            <div className="bg-white rounded-lg shadow-2xl w-full max-w-[95vw] sm:max-w-7xl max-h-[95vh] overflow-auto">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 border-b bg-gray-50 gap-3 sm:gap-0">
                    <div className="flex-1">
                        <h2
                            className="text-lg sm:text-2xl font-bold text-gray-800"
                            style={{ fontFamily: "NotoSansThai, Arial, sans-serif" }}
                        >
                            ใบประกาศนียบัตรเส้นทางการเรียน
                        </h2>
                        <p
                            className="text-sm sm:text-base text-gray-600 mt-1"
                            style={{ fontFamily: "NotoSansThai, Arial, sans-serif" }}
                        >
                            เส้นทาง: {pathName}
                        </p>
                    </div>
                    <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto">
                        {isReady && (
                            <>
                                <button
                                    onClick={downloadCertificate}
                                    disabled={isGenerating}
                                    className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors shadow-md disabled:opacity-50 text-xs sm:text-sm flex-1 sm:flex-none justify-center"
                                    style={{ fontFamily: "NotoSansThai, Arial, sans-serif" }}
                                >
                                    {isGenerating ? (
                                        <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-t-2 border-white"></div>
                                    ) : (
                                        <Download size={14} className="sm:w-4 sm:h-4" />
                                    )}
                                    PNG
                                </button>

                                <button
                                    onClick={downloadPDF}
                                    disabled={isGenerating}
                                    className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-md disabled:opacity-50 text-xs sm:text-sm flex-1 sm:flex-none justify-center"
                                    style={{ fontFamily: "NotoSansThai, Arial, sans-serif" }}
                                >
                                    {isGenerating ? (
                                        <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-t-2 border-white"></div>
                                    ) : (
                                        <Download size={14} className="sm:w-4 sm:h-4" />
                                    )}
                                    PDF
                                </button>
                            </>
                        )}
                        <button onClick={onClose} className="p-2 hover:bg-gray-200 rounded-full transition-colors">
                            <X size={20} className="sm:w-6 sm:h-6" />
                        </button>
                    </div>
                </div>

                <div className="p-2 sm:p-8 bg-white">
                    {!isReady ? (
                        <div className="flex flex-col items-center justify-center h-48 sm:h-64">
                            <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-t-2 border-b-2 border-[#008268] mb-4"></div>
                            <span
                                className="text-sm sm:text-base text-gray-600 text-center px-4"
                                style={{ fontFamily: "NotoSansThai, Arial, sans-serif" }}
                            >
                                กำลังโหลดใบประกาศนียบัตรเส้นทางการเรียน...
                            </span>
                            <div
                                className="mt-2 text-xs sm:text-sm text-gray-500 text-center"
                                style={{ fontFamily: "NotoSansThai, Arial, sans-serif" }}
                            >
                                <p>📝 โหลดฟอนต์: {fontLoaded ? "✅" : "⏳"}</p>
                                <p>🖼️ โหลดรูปภาพ: {imageLoaded ? "✅" : "⏳"}</p>
                                <p>🏢 โหลดโลโก้: {logoLoaded ? "✅" : "⏳"}</p>
                            </div>
                        </div>
                    ) : (
                        <>

                            <div className="flex justify-center items-center">
                                <div
                                    ref={certificateRef}
                                    className="relative w-full max-w-[95vw] sm:max-w-4xl lg:max-w-6xl"
                                    style={{
                                        aspectRatio: "1200/800",
                                        fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                    }}
                                >

                                    <div
                                        className="absolute inset-0 bg-cover bg-center bg-no-repeat rounded-lg overflow-hidden"
                                        style={{
                                            backgroundImage: "url(/e-med/img/PathCertificate.png)",
                                            backgroundSize: "100% 100%",
                                        }}
                                    ></div>


                                    <div
                                        className="absolute bg-cover bg-center bg-no-repeat"
                                        style={{
                                            backgroundImage: "url(/e-med/img/logo-1.png)",
                                            backgroundSize: "contain",
                                            top: "7.5%",
                                            left: "7.5%",
                                            width: "20.8%",
                                            height: "17.5%",
                                        }}
                                    ></div>


                                    <div className="absolute inset-0">
                                        <div
                                            className="absolute text-[#424242] text-left"
                                            style={{
                                                top: "23.75%",
                                                left: "8.33%",
                                                fontSize: "clamp(1rem, 4.17vw, 3.125rem)",
                                                width: "55%",
                                                lineHeight: "1.3",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            ประกาศนียบัตร
                                        </div>
                                        <div
                                            className="absolute text-[#424242] text-left"
                                            style={{
                                                top: "33.75%",
                                                left: "8.33%",
                                                fontSize: "clamp(0.625rem, 2.5vw, 1.875rem)",
                                                width: "55%",
                                                lineHeight: "1.3",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            ขอมอบประกาศนียบฉบับนี้เพื่อแสดงว่า
                                        </div>
                                        <div
                                            className="absolute text-[#008268] text-left font-bold"
                                            style={{
                                                top: "43.75%",
                                                left: "8.33%",
                                                fontSize: "clamp(0.875rem, 3.75vw, 2.8125rem)",
                                                width: "80%",
                                                lineHeight: "1.2",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            {userName}
                                        </div>
                                        <div
                                            className="absolute text-[#424242] text-left"
                                            style={{
                                                top: "56.25%",
                                                left: "8.33%",
                                                fontSize: "clamp(0.625rem, 2.5vw, 1.875rem)",
                                                width: "80%",
                                                lineHeight: "1.3",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            ได้สำเร็จเส้นทางการเรียนรู้ผ่าน Synphaet E-MED Learning
                                        </div>
                                        <div
                                            className="absolute text-[#424242] text-left"
                                            style={{
                                                top: "62.5%",
                                                left: "8.33%",
                                                fontSize: "clamp(0.625rem, 2.5vw, 1.875rem)",
                                                width: "80%",
                                                lineHeight: "1.3",
                                                wordWrap: "break-word",
                                                whiteSpace: "normal",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            <span className="font-thin">ในเส้นทาง </span>
                                            <span className="font-bold">"{pathName}"</span>
                                        </div>
                                        <div
                                            className="absolute text-[#424242] font-medium"
                                            style={{
                                                bottom: "9.625%",
                                                right: "12.5%",
                                                fontSize: "clamp(0.5rem, 2vw, 1.5rem)",
                                                textAlign: "right",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            เมื่อวันที่ {formattedDate}
                                        </div>
                                        <div
                                            className="absolute text-[#424242] font-medium text-left"
                                            style={{
                                                bottom: "11.25%",
                                                left: "10.42%",
                                                fontSize: "clamp(0.5rem, 1.67vw, 1.25rem)",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            ดร.มาเซอลีน เกียรติ์ชัยสิริพร
                                        </div>
                                        <div
                                            className="absolute text-[#666666] font-medium text-left"
                                            style={{
                                                bottom: "7.5%",
                                                left: "8.33%",
                                                fontSize: "clamp(0.5rem, 1.67vw, 1.25rem)",
                                                fontFamily: "NotoSansThai, 'Noto Sans Thai', Arial, sans-serif",
                                            }}
                                        >
                                            (ผู้อำนวยการโรงพยาบาลสินแพทย์)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}

export default PathCertificate
