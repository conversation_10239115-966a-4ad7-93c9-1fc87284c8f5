"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Masonry from "react-masonry-css"
import { Clock, Award, CheckCircle, Lock, ChevronRight, Play, ChevronDown, Route, FileBadge } from "lucide-react"
import { learningPathsData } from "@/data/learningPaths"
import { getCoursesData } from "@/data/allCourses"
import { getPathsAssignedToUser } from "@/data/assignedPaths"
import { getUserProgress } from "@/data/userProgress"
import PathCertificate from "./PathCertificate"
import { getMedicalUsersData } from "@/data/allUsers"

// Types
interface Course {
  id: string
  name: string
  duration: number
  difficulty: string
  isCompleted: boolean
  isUnlocked: boolean
  progress: number
}

interface Pathway {
  id: string
  name: string
  description: string
  specialty: string
  difficulty: string
  estimatedDuration: number
  completedCourses: number
  totalCourses: number
  progressPercentage: number
  courses: Course[]
}

interface LearningPathsCardProps {
  accordionMode?: boolean
  defaultExpanded?: string[]
}

const getDifficultyText = (difficulty: string): string => {
  const difficultyMap: { [key: string]: string } = {
    beginner: "เบื้องต้น",
    intermediate: "ปานกลาง",
    advanced: "ยาก",
    expert: "ผู้เชี่ยวชาญ",
  }
  return difficultyMap[difficulty] || difficulty
}

const getDifficultyIndicators = (difficulty: string) => {
  const level = getDifficultyText(difficulty)

  return (
    <div className="flex gap-0.5 ml-1">
      {["เบื้องต้น", "ปานกลาง", "ยาก"].map((l, i) => {
        let circleColor = "bg-gray-200"
        if (level === "เบื้องต้น") {
          if (i === 0) circleColor = "bg-green-500"
        } else if (level === "ปานกลาง") {
          if (i === 0) circleColor = "bg-[#ffce66]"
          if (i === 1) circleColor = "bg-[#FFB211]"
        } else if (level === "ยาก") {
          if (i === 0) circleColor = "bg-red-200"
          if (i === 1) circleColor = "bg-red-300"
          if (i === 2) circleColor = "bg-red-500"
        } else if (level === "ผู้เชี่ยวชาญ") {
          if (i === 0) circleColor = "bg-purple-300"
          if (i === 1) circleColor = "bg-purple-400"
          if (i === 2) circleColor = "bg-purple-600"
        }
        return <div key={l} className={`w-2 h-2 rounded-full ${circleColor}`} />
      })}
    </div>
  )
}

const getProgressGradient = (percentage: number): string => {
  if (percentage === 0) return "from-gray-300 to-gray-300"
  if (percentage <= 25) return "from-red-500 to-red-400"
  if (percentage <= 50) return "from-orange-500 to-orange-400"
  if (percentage <= 75) return "from-yellow-500 to-yellow-400"
  return "from-green-500 to-green-400"
}

const LearningPathsCard: React.FC<LearningPathsCardProps> = ({ accordionMode = false, defaultExpanded = [] }) => {
  const router = useRouter()
  const [expandedPathways, setExpandedPathways] = useState<Set<string>>(new Set(defaultExpanded))
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [userPathways, setUserPathways] = useState<Pathway[]>([])
  const [showCertificate, setShowCertificate] = useState(false)
  const [selectedPathway, setSelectedPathway] = useState<Pathway | null>(null)
  const [currentUser, setCurrentUser] = useState<any>(null)

  useEffect(() => {
    const userId = localStorage.getItem("userId")
    setCurrentUserId(userId)

    if (userId) {
      const allUsers = getMedicalUsersData
      const userData = allUsers.find((user) => user.id === userId)
      setCurrentUser(userData)
      console.log("Current user data:", userData) // Debug log
    }
  }, [])

  useEffect(() => {
    if (!currentUserId) return

    const convertToUserPathways = (): Pathway[] => {
      const allCourses = getCoursesData
      const userProgress = getUserProgress(currentUserId)
      const assignedPathIds = getPathsAssignedToUser(currentUserId)

      // Filter only assigned paths
      const assignedPaths = learningPathsData.filter((path) => assignedPathIds.includes(path.id))

      return assignedPaths.map((path) => {
        // Get courses for this path with real progress
        const pathCourses = path.courseIds
          .map((courseId, index) => {
            const course = allCourses.find((c) => c.id === courseId)
            if (!course) return null

            // Get user progress for this course
            const courseProgress = userProgress.find((p) => p.courseId === courseId)

            return {
              id: course.id,
              name: course.name,
              duration: Math.round(course.time / 3600),
              difficulty: course.level,
              isCompleted: courseProgress?.status === "completed",
              isUnlocked: index === 0 || courseProgress?.status !== "not_started",
              progress: courseProgress?.progress || 0,
            }
          })
          .filter(Boolean) as Course[]

        // Calculate real progress
        const completedCourses = pathCourses.filter((c) => c.isCompleted).length
        const totalProgress = pathCourses.reduce((sum, course) => sum + course.progress, 0)
        const progressPercentage = pathCourses.length > 0 ? Math.round(totalProgress / pathCourses.length) : 0

        // Calculate total duration
        const estimatedDuration = pathCourses.reduce((total, course) => total + course.duration, 0)

        // Determine specialty and difficulty based on courses
        const specialty = path.id.includes("emergency")
          ? "emergency"
          : path.id.includes("nursing")
            ? "nursing"
            : path.id.includes("geriatrics")
              ? "geriatrics"
              : path.id.includes("infection")
                ? "infection"
                : "general"

        const difficulty = pathCourses.length <= 1 ? "beginner" : pathCourses.length <= 2 ? "intermediate" : "advanced"

        return {
          id: path.id,
          name: path.name,
          description: path.description,
          specialty,
          difficulty,
          estimatedDuration,
          completedCourses,
          totalCourses: pathCourses.length,
          progressPercentage,
          courses: pathCourses,
        }
      })
    }

    setUserPathways(convertToUserPathways())
  }, [currentUserId])

  const togglePathway = (pathwayId: string) => {
    setExpandedPathways((prev) => {
      const newSet = new Set(prev)

      if (accordionMode) {
        // Accordion mode: เปิดทีละอัน
        if (newSet.has(pathwayId)) {
          newSet.clear()
        } else {
          newSet.clear()
          newSet.add(pathwayId)
        }
      } else {
        // Multi-expand mode: เปิดได้หลายอัน
        if (newSet.has(pathwayId)) {
          newSet.delete(pathwayId)
        } else {
          newSet.add(pathwayId)
        }
      }

      return newSet
    })
  }

  const handleShowCertificate = (pathway: Pathway) => {
    setSelectedPathway(pathway)
    setShowCertificate(true)
  }

  const expandAll = () => {
    if (!accordionMode) {
      setExpandedPathways(new Set(userPathways.map((p) => p.id)))
    }
  }

  const collapseAll = () => {
    setExpandedPathways(new Set())
  }

  const allExpanded = expandedPathways.size === userPathways.length
  const noneExpanded = expandedPathways.size === 0

  // Masonry breakpoints
  const breakpointColumnsObj = {
    default: 2,
    1100: 2,
    700: 1,
    500: 1,
  }

  if (!currentUserId) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดข้อมูล...</p>
        </div>
      </div>
    )
  }

  if (userPathways.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Route size={48} className="text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">ไม่มีเส้นทางการเรียนรู้</h3>
          <p className="text-gray-600 mb-4">คุณยังไม่ได้รับมอบหมายเส้นทางการเรียนรู้ใดๆ</p>
          <Link
            href="/courses"
            className="inline-flex items-center px-4 py-2 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
          >
            ดูคอร์สทั้งหมด
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Masonry Layout */}
      <Masonry
        breakpointCols={breakpointColumnsObj}
        className="flex w-auto -ml-6"
        columnClassName="pl-6 bg-clip-padding"
      >
        {userPathways.map((pathway, index) => {
          const isExpanded = expandedPathways.has(pathway.id)

          return (
            <div
              key={pathway.id}
              className={`mb-6 bg-white rounded-lg shadow-sm border transition-all duration-300 ${
                isExpanded ? "shadow-lg border-[#008268]/20" : "hover:shadow-md hover:border-gray-300"
              }`}
              style={{
                breakInside: "avoid",
                pageBreakInside: "avoid",
              }}
            >
              {/* Pathway Header - Clickable */}
              <div
                className={`p-4 border-b border-gray-100 cursor-pointer transition-all duration-200 ${
                  isExpanded ? "bg-gradient-to-r from-[#008268]/5 to-transparent" : "hover:bg-gray-50"
                }`}
                onClick={() => togglePathway(pathway.id)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault()
                    togglePathway(pathway.id)
                  }
                }}
                aria-expanded={isExpanded}
                aria-controls={`pathway-content-${pathway.id}`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-7 h-7 bg-gradient-to-br from-[#008268] to-[#006e58] rounded-lg flex items-center justify-center mr-3">
                          <Route size={20} className="text-white" />
                        </div>
                        <h3 className="text-lg font-semibold mt-2 text-gray-900 mb-2 pr-3">{pathway.name}</h3>
                      </div>
                      <div className="flex items-center">
                        <ChevronDown
                          size={18}
                          className={`text-gray-400 transition-all duration-300 ${
                            isExpanded ? "rotate-180 text-[#008268]" : "hover:text-gray-600"
                          }`}
                        />
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm mb-3">{pathway.description}</p>
                    <div className="flex items-center gap-2 mb-3 flex-wrap">
                      <span className="text-xs px-2 py-1 bg-[#008268]/10 text-[#008268] rounded-full transition-transform hover:scale-105 flex items-center">
                        <Route size={10} className="mr-1" />
                        {pathway.totalCourses} คอร์ส
                      </span>
                      <span className="text-xs text-gray-500 flex items-center transition-transform hover:scale-105">
                        <Clock size={10} className="mr-1" />
                        {pathway.estimatedDuration} ชั่วโมง
                      </span>
                    </div>
                  </div>
                  <div className="ml-3">
                    {pathway.progressPercentage === 100 ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleShowCertificate(pathway)
                        }}
                        className="w-12 h-12 bg-gradient-to-br from-[#008268] to-[#006e58] rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-110 hover:rotate-3 shadow-md hover:shadow-lg"
                        title="ดูใบประกาศนียบัตร"
                      >
                        <FileBadge size={20} className="text-white" />
                      </button>
                    ) : (
                      <div
                        className="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg flex items-center justify-center cursor-not-allowed opacity-60"
                        title="ทำให้เสร็จเพื่อรับใบประกาศนียบัตร"
                      >
                        <Lock size={20} className="text-white" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Progress with Color Gradient */}
                <div className="mb-3">
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>ความก้าวหน้า</span>
                    <span>
                      {pathway.completedCourses} จาก {pathway.totalCourses} คอร์ส
                    </span>
                  </div>
                  <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full bg-gradient-to-r ${getProgressGradient(pathway.progressPercentage)} rounded-full transition-all duration-1000 ease-out shadow-sm`}
                      style={{
                        width: `${pathway.progressPercentage}%`,
                        transitionDelay: `${index * 0.1}s`,
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <div className="text-xs text-gray-500">
                      {pathway.progressPercentage === 0 && "ยังไม่เริ่ม"}
                      {pathway.progressPercentage > 0 && pathway.progressPercentage <= 25 && "เริ่มต้น"}
                      {pathway.progressPercentage > 25 && pathway.progressPercentage <= 50 && "กำลังดำเนินการ"}
                      {pathway.progressPercentage > 50 && pathway.progressPercentage <= 75 && "ใกล้เสร็จ"}
                      {pathway.progressPercentage > 75 && pathway.progressPercentage < 100 && "เกือบเสร็จ"}
                      {pathway.progressPercentage === 100 && "เสร็จสมบูรณ์"}
                    </div>
                    <div
                      className={`text-sm font-semibold transition-all duration-300 ${
                        pathway.progressPercentage === 0
                          ? "text-gray-500"
                          : pathway.progressPercentage <= 25
                            ? "text-red-600"
                            : pathway.progressPercentage <= 50
                              ? "text-orange-600"
                              : pathway.progressPercentage <= 75
                                ? "text-yellow-600"
                                : "text-[#008268]"
                      }`}
                    >
                      {pathway.progressPercentage}%
                    </div>
                  </div>
                </div>
              </div>

              {/* Collapsible Content */}
              <div
                id={`pathway-content-${pathway.id}`}
                className={`overflow-hidden transition-all duration-500 ease-in-out ${
                  isExpanded ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
                }`}
              >
                <div className="p-4 bg-gradient-to-b from-transparent to-gray-50/30">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <div className="w-1 h-5 bg-[#008268] rounded-full mr-2"></div>
                    คอร์สในเส้นทางการเรียน
                  </h4>
                  <div className="space-y-3 relative">
                    {pathway.courses.map((course, courseIndex) => (
                      <div
                        key={course.id}
                        className={`flex items-center relative p-2 rounded-lg transition-all duration-200 ${
                          course.isUnlocked ? "hover:bg-white hover:shadow-sm hover:scale-102 hover:translate-x-1" : ""
                        }`}
                        style={{
                          animationDelay: `${courseIndex * 0.1}s`,
                        }}
                      >
                        {/* Step Number */}
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold mr-3 z-10 transition-all duration-200 hover:scale-110 ${
                            course.isCompleted
                              ? "bg-green-100 text-green-800 shadow-sm"
                              : course.isUnlocked
                                ? "bg-[#008268] text-white shadow-md"
                                : "bg-gray-100 text-gray-400"
                          }`}
                        >
                          {course.isCompleted ? (
                            <CheckCircle size={14} />
                          ) : course.isUnlocked ? (
                            courseIndex + 1
                          ) : (
                            <Lock size={14} />
                          )}
                        </div>

                        {/* Course Info */}
                        <div className="flex-1 min-w-0">
                          <div
                            className={`font-medium mb-1 text-sm truncate ${course.isUnlocked ? "text-gray-900" : "text-gray-400"}`}
                          >
                            {course.name}
                          </div>
                          <div className="text-xs text-gray-500 flex items-center mb-1">
                            <Clock size={10} className="mr-1 flex-shrink-0" />
                            {course.duration} ชั่วโมง
                            <span className="mx-1">•</span>
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-gray-500 truncate">
                                {getDifficultyText(course.difficulty)}
                              </span>
                              <div className="flex gap-0.5 ml-1">
                                {["เบื้องต้น", "ปานกลาง", "ยาก"].map((l, i) => {
                                  let circleColor = "bg-gray-200"
                                  const level = getDifficultyText(course.difficulty)
                                  if (level === "เบื้องต้น") {
                                    if (i === 0) circleColor = "bg-green-500"
                                  } else if (level === "ปานกลาง") {
                                    if (i === 0) circleColor = "bg-[#ffce66]"
                                    if (i === 1) circleColor = "bg-[#FFB211]"
                                  } else if (level === "ยาก") {
                                    if (i === 0) circleColor = "bg-red-200"
                                    if (i === 1) circleColor = "bg-red-300"
                                    if (i === 2) circleColor = "bg-red-500"
                                  } else if (level === "ผู้เชี่ยวชาญ") {
                                    if (i === 0) circleColor = "bg-purple-300"
                                    if (i === 1) circleColor = "bg-purple-400"
                                    if (i === 2) circleColor = "bg-purple-600"
                                  }
                                  return <div key={l} className={`w-1.5 h-1.5 rounded-full ${circleColor}`} />
                                })}
                              </div>
                            </div>
                          </div>
                          {course.progress > 0 && (
                            <div className="flex items-center gap-2 mt-1">
                              <div className="flex-1 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-[#008268] to-[#4ade80] rounded-full transition-all duration-300"
                                  style={{ width: `${course.progress}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-[#008268] font-medium min-w-[30px]">
                                {course.progress}%
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Action Button */}
                        <div className="ml-2 flex-shrink-0">
                          {course.isCompleted ? (
                            <Link
                              href={`/courses/${course.id}`}
                              className="text-[#293D97] hover:text-blue-700 text-xs font-medium px-2 py-1 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105"
                              onClick={(e) => e.stopPropagation()}
                            >
                              ดูรายละเอียด
                            </Link>
                          ) : course.isUnlocked ? (
                            <Link
                              href={`/courses/${course.id}`}
                              className="text-[#008268] hover:text-[#006e58] text-xs font-medium flex items-center px-2 py-1 rounded-md hover:bg-green-50 transition-all duration-200 hover:scale-105"
                              onClick={(e) => e.stopPropagation()}
                            >
                              {course.progress > 0 ? "เรียนต่อ" : "เริ่มเรียน"}
                              <ChevronRight size={12} className="ml-1" />
                            </Link>
                          ) : (
                            <span className="text-gray-400 text-xs px-2 py-1">ล็อค</span>
                          )}
                        </div>

                        {/* Connecting Line */}
                        {courseIndex < pathway.courses.length - 1 && (
                          <div className="absolute left-5 top-8 w-0.5 h-6 bg-gradient-to-b from-gray-300 to-gray-200 z-0"></div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <div className="flex gap-2">
                      {pathway.progressPercentage === 100 ? (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShowCertificate(pathway)
                          }}
                          className="flex-1 flex items-center justify-center px-3 py-2 bg-gradient-to-r from-[#008268] to-[#006e58] text-white rounded-lg hover:from-[#006e58] hover:to-[#005f47] transition-all duration-200 shadow-md hover:shadow-lg hover:scale-102 text-sm"
                        >
                          <Award size={14} className="mr-2" />
                          ดูใบประกาศนียบัตร
                        </button>
                      ) : pathway.progressPercentage > 0 ? (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            const nextCourse = pathway.courses.find((c) => !c.isCompleted && c.isUnlocked)
                            if (nextCourse) {
                              router.push(`/courses/${nextCourse.id}`)
                            }
                          }}
                          className="flex-1 flex items-center justify-center px-3 py-2 bg-[#5A69AF] text-white rounded-lg hover:bg-[#1e2c6c] transition-all duration-200 shadow-md hover:shadow-lg hover:scale-102 text-sm"
                        >
                          <Play size={14} className="mr-2" />
                          เรียนต่อ
                        </button>
                      ) : (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            const firstCourse = pathway.courses[0]
                            if (firstCourse) {
                              router.push(`/courses/${firstCourse.id}`)
                            }
                          }}
                          className="flex-1 flex items-center justify-center px-3 py-2 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-all duration-200 shadow-md hover:shadow-lg hover:scale-105 text-sm"
                        >
                          <Play size={14} className="mr-2" />
                          เริ่มเส้นทางการเรียน
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </Masonry>

      {/* Path Certificate Modal */}
      <div className="-space-y-6">
      {showCertificate && selectedPathway && currentUser && (
        <PathCertificate
          userName={`${currentUser.firstname}${currentUser.lastname ? " " + currentUser.lastname : ""}`.trim()}
          pathName={selectedPathway.name}
          completionDate={new Date()}
          isOpen={showCertificate}
          onClose={() => {
            setShowCertificate(false)
            setSelectedPathway(null)
          }}
        />
        
      )}
      </div>

      {/* Expand/Collapse All Buttons */}
      {!accordionMode && (
        <div className="flex justify-end space-x-2 mt-4">
          <button
            onClick={expandAll}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              allExpanded ? "bg-gray-300 text-gray-600 cursor-not-allowed" : "bg-[#008268] text-white hover:bg-[#006e58]"
            }`}
            disabled={allExpanded}
          >
            ขยายทั้งหมด
          </button>
          <button
            onClick={collapseAll}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              noneExpanded ? "bg-gray-300 text-gray-600 cursor-not-allowed" : "bg-[#008268] text-white hover:bg-[#006e58]"
            }`}
            disabled={noneExpanded}
          >
            ยุบทั้งหมด
          </button>
        </div>
      )}
      {/* Custom Masonry Styles */}
      <style jsx>{`
        .masonry-grid {
          display: flex;
          margin-left: -24px;
          width: auto;
        }
        .masonry-grid_column {
          padding-left: 24px;
          background-clip: padding-box;
        }
        .masonry-grid_column > div {
          margin-bottom: 24px;
        }
      `}</style>
    </div>
  )
}

export default LearningPathsCard
