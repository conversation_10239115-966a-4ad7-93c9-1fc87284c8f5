"use client"

import { useEffect, useState } from "react"
import { getCoursesData } from "@/data/allCourses"
import HrawersLearming from "./hrawers"
import LearmingContent from "./content"
import { hasFinalExam } from "@/data/finalExamQuestions" // แก้ไข import path
import { CheckCircle, X } from "lucide-react"

export default function Learming({ coursesID }: { coursesID: string }) {
  const [courses, setCourses] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [menuOpen, setMenuOpen] = useState(false)
  const [selectId, setSelectId] = useState<{ lessonId: string; contentId: string }>({
    lessonId: "",
    contentId: "",
  })
  const [userProgress, setUserProgress] = useState<{
    completedContents: string[]
    completedLessons: string[]
    allLessonsCompleted: boolean
  }>({
    completedContents: [],
    completedLessons: [],
    allLessonsCompleted: false,
  })
  const [userId, setUserId] = useState<string>("")

  // ตรวจสอบว่าคอร์สนี้มีข้อสอบท้ายบทหรือไม่
  const [hasFinalExamState, setHasFinalExamState] = useState(false)

  // เพิ่มสถานะสำหรับป๊อปอัพแจ้งเตือนปลดล็อกข้อสอบท้ายบท
  const [showUnlockExamPopup, setShowUnlockExamPopup] = useState(false)

  // เพิ่มตัวแปรเพื่อติดตามว่าเคยแสดงป๊อปอัพแล้วหรือยัง
  const [examUnlockPopupShown, setExamUnlockPopupShown] = useState(false)

  // ฟังก์ชันสำหรับสร้าง key ที่ใช้ใน localStorage โดยรวม userId
  const getStorageKey = (key: string) => {
    return `${key}_${userId}`
  }

  // แก้ไขฟังก์ชัน checkAllLessonsCompleted ให้มีการ log ข้อมูลมากขึ้น
  const checkAllLessonsCompleted = () => {
    if (!courses || !courses.lesson || !userId) return false

    try {
      const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")

      // สร้างรายการเนื้อหาทั้งหมดที่ต้องทำให้เสร็จ
      const allRequiredContents = courses.lesson.flatMap((lesson: any) =>
        lesson.content.map((content: any) => content.id),
      )

      // ตรวจสอบว่าทุกเนื้อหาในทุกบทเรียนถูกทำเสร็จแล้วหรือไม่
      const allCompleted = allRequiredContents.every((contentId: any) => completedContents.includes(contentId))

      console.log("Check all lessons completed (detailed):", {
        completedContents,
        allRequiredContents,
        allCompleted,
        missingContents: allRequiredContents.filter((id: any) => !completedContents.includes(id)),
        userId,
      })

      return allCompleted
    } catch (e) {
      console.error("Error checking all lessons completed:", e)
      return false
    }
  }

  // แก้ไขฟังก์ชัน handleContentComplete เพื่อให้ตรวจสอบและแสดง popup ทันทีหลังจากทำบทเรียนสุดท้ายเสร็จ
  const handleContentComplete = (result: { contentId: string; completed: boolean }) => {
    if (!result.completed || !courses || !userId) return

    console.log("Content completed:", result.contentId)

    // อัปเดต state
    setUserProgress((prev) => {
      // ถ้าเนื้อหานี้ยังไม่ถูกมาร์กว่าเสร็จแล้ว
      if (!prev.completedContents.includes(result.contentId)) {
        const newCompletedContents = [...prev.completedContents, result.contentId]

        // ตรวจสอบว่าทุกเนื้อหาในบทเรียนนี้ถูกทำเสร็จแล้วหรือยัง
        const currentLesson = courses.lesson.find((l: any) => l.id === selectId.lessonId)
        const allContentInLessonCompleted =
          currentLesson && currentLesson.content.every((c: any) => newCompletedContents.includes(c.id))

        // อัปเดตบทเรียนที่เสร็จแล้ว
        let newCompletedLessons = [...prev.completedLessons]
        if (allContentInLessonCompleted && !prev.completedLessons.includes(selectId.lessonId)) {
          newCompletedLessons = [...newCompletedLessons, selectId.lessonId]
        }

        // บันทึกลง localStorage โดยใช้ userId
        if (typeof window !== "undefined") {
          localStorage.setItem(getStorageKey("completedContents"), JSON.stringify(newCompletedContents))
          localStorage.setItem(getStorageKey("completedLessons"), JSON.stringify(newCompletedLessons))
        }

        // ตรวจสอบว่าทำทุกบทเรียนเสร็จแล้วหรือยัง - ตรวจสอบโดยตรงจากข้อมูลปัจจุบัน
        // ไม่ใช้ checkAllLessonsCompleted() เพื่อให้ใช้ข้อมูลล่าสุดที่เพิ่งอัปเดต
        const allLessonsCompleted = courses.lesson.every((lesson: any) => {
          return lesson.content.every((content: any) => newCompletedContents.includes(content.id))
        })

        console.log("Updated progress:", {
          completedContents: newCompletedContents.length,
          completedLessons: newCompletedLessons.length,
          allLessonsCompleted,
          userId,
          totalLessons: courses.lesson.length,
          totalContents: courses.lesson.reduce((acc: number, lesson: any) => acc + lesson.content.length, 0),
        })

        // ถ้าทำทุกบทเรียนเสร็จแล้ว และมีข้อสอบท้ายบท และยังไม่เคยแสดงป๊อปอัพ
        if (allLessonsCompleted && hasFinalExamState) {
          // ตรวจสอบว่าเคยแสดงป๊อปอัพแล้วหรือยัง
          const popupKey = getStorageKey(`examUnlockPopupShown_${coursesID}`)
          const popupShown = localStorage.getItem(popupKey) === "true"

          console.log("Popup check after completion:", { popupKey, popupShown, allLessonsCompleted })

          if (!popupShown) {
            console.log("Showing unlock exam popup immediately")
            setShowUnlockExamPopup(true)
            setExamUnlockPopupShown(true)
            localStorage.setItem(popupKey, "true")
          }
        }

        return {
          completedContents: newCompletedContents,
          completedLessons: newCompletedLessons,
          allLessonsCompleted,
        }
      }
      return prev
    })
  }

  // แก้ไขฟังก์ชันที่ตรวจสอบว่าทำทุกบทเรียนเสร็จแล้วหรือยัง
  useEffect(() => {
    // ดึง userId จาก localStorage
    const fetchUserId = () => {
      if (typeof window !== "undefined") {
        const id = localStorage.getItem("userId")
        if (id) {
          setUserId(id)
          return id
        } else {
          // ถ้าไม่มี userId ให้สร้างใหม่และบันทึก
          const newId = `user_${Date.now()}`
          localStorage.setItem("userId", newId)
          setUserId(newId)
          return newId
        }
      }
      return ""
    }

    const currentUserId = fetchUserId()
    console.log("Current User ID:", currentUserId)

    const fetchCourse = () => {
      try {
        const courseData = getCoursesData.find((course) => course.id === coursesID)
        setCourses(courseData)

        // ตรวจสอบว่ามีข้อสอบท้ายบทหรือไม่
        const hasExam = hasFinalExam(coursesID)
        setHasFinalExamState(hasExam)
        console.log("Course has final exam:", hasExam)

        // ถ้ามีข้อมูลคอร์ส ให้เลือกบทเรียนแรกและเนื้อหาแรก
        if (courseData && courseData.lesson && courseData.lesson.length > 0) {
          const firstLesson = courseData.lesson[0]
          if (firstLesson.content && firstLesson.content.length > 0) {
            setSelectId({
              lessonId: firstLesson.id,
              contentId: firstLesson.content[0].id,
            })
          }
        }

        // โหลดความก้าวหน้าของผู้ใช้จาก localStorage
        if (typeof window !== "undefined" && currentUserId) {
          try {
            const storageKey = `completedContents_${currentUserId}`
            const completedContents = JSON.parse(localStorage.getItem(storageKey) || "[]")

            const lessonsKey = `completedLessons_${currentUserId}`
            const completedLessons = JSON.parse(localStorage.getItem(lessonsKey) || "[]")

            console.log("Loaded progress from localStorage:", {
              storageKey,
              completedContents,
              lessonsKey,
              completedLessons,
              userId: currentUserId,
            })

            // ตรวจสอบว่าทำทุกบทเรียนเสร็จแล้วหรือยัง
            let allLessonsCompleted = false

            if (courseData && courseData.lesson && courseData.lesson.length > 0) {
              allLessonsCompleted = courseData.lesson.every((lesson: any) => {
                // ตรวจสอบว่าทุกเนื้อหาในบทเรียนนี้ถูกทำเสร็จแล้วหรือยัง
                return lesson.content.every((c: any) => completedContents.includes(c.id))
              })
            }

            console.log("Initial progress check:", {
              completedContents,
              completedLessons,
              allLessonsCompleted,
              userId: currentUserId,
            })

            setUserProgress({
              completedContents,
              completedLessons,
              allLessonsCompleted,
            })

            // ตรวจสอบว่าเคยแสดงป๊อปอัพแล้วหรือยัง
            const popupKey = `examUnlockPopupShown_${coursesID}_${currentUserId}`
            const popupShown = localStorage.getItem(popupKey) === "true"
            setExamUnlockPopupShown(popupShown)

            console.log("Popup check:", {
              popupKey,
              popupShown,
              allLessonsCompleted,
              hasExam,
            })

            // ถ้าทำทุกบทเรียนเสร็จแล้ว และมีข้อสอบท้ายบท และยังไม่เคยแสดงป๊อปอัพ
            if (allLessonsCompleted && hasExam && !popupShown) {
              console.log("Should show popup on load")
              setShowUnlockExamPopup(true)
              setExamUnlockPopupShown(true)
              localStorage.setItem(popupKey, "true")
            }
          } catch (e) {
            console.error("Error reading from localStorage:", e)
          }
        }
      } catch (error) {
        console.error("Error fetching course:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchCourse()
  }, [coursesID])

  // เพิ่ม useEffect เพื่อตรวจสอบความก้าวหน้าเมื่อ userId เปลี่ยน
  useEffect(() => {
    if (userId && courses) {
      // ตรวจสอบความก้าวหน้าใหม่เมื่อ userId เปลี่ยน
      const checkProgress = () => {
        try {
          const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")
          const completedLessons = JSON.parse(localStorage.getItem(getStorageKey("completedLessons")) || "[]")

          // ตรวจสอบว่าทำทุกบทเรียนเสร็จแล้วหรือยัง
          const allLessonsCompleted = checkAllLessonsCompleted()

          console.log("Checking progress after userId change:", {
            completedContents,
            completedLessons,
            allLessonsCompleted,
            userId,
          })

          setUserProgress({
            completedContents,
            completedLessons,
            allLessonsCompleted,
          })

          // ตรวจสอบว่าเคยแสดงป๊อปอัพแล้วหรือยัง
          const popupShown = localStorage.getItem(getStorageKey(`examUnlockPopupShown_${coursesID}`)) === "true"
          setExamUnlockPopupShown(popupShown)

          // ถ้าทำทุกบทเรียนเสร็จแล้ว และมีข้อสอบท้ายบท และยังไม่เคยแสดงป๊อปอัพ
          if (allLessonsCompleted && hasFinalExamState && !popupShown) {
            console.log("Should show popup after userId change")
            setShowUnlockExamPopup(true)
            setExamUnlockPopupShown(true)
            localStorage.setItem(getStorageKey(`examUnlockPopupShown_${coursesID}`), "true")
          }
        } catch (e) {
          console.error("Error checking progress after userId change:", e)
        }
      }

      checkProgress()
    }
  }, [userId, courses, hasFinalExamState, coursesID])

  // เพิ่ม useEffect เพื่อตรวจสอบความก้าวหน้าเมื่อ completedContents เปลี่ยนแปลง
  // เพิ่มหลังจาก useEffect ที่มีอยู่แล้ว
  useEffect(() => {
    // ตรวจสอบเมื่อ userProgress.completedContents เปลี่ยนแปลง
    if (userId && courses && userProgress.completedContents.length > 0) {
      // ตรวจสอบว่าทำทุกบทเรียนเสร็จแล้วหรือยัง
      const allCompleted = courses.lesson.every((lesson: any) => {
        return lesson.content.every((content: any) => userProgress.completedContents.includes(content.id))
      })

      console.log("Checking progress after completedContents change:", {
        completedContentsCount: userProgress.completedContents.length,
        allCompleted,
        totalContents: courses.lesson.reduce((acc: number, lesson: any) => acc + lesson.content.length, 0),
      })

      // ถ้าทำทุกบทเรียนเสร็จแล้ว และมีข้อสอบท้ายบท และยังไม่เคยแสดงป๊อปอัพ
      if (allCompleted && hasFinalExamState && !examUnlockPopupShown) {
        console.log("Should show popup after completedContents change")
        setShowUnlockExamPopup(true)
        setExamUnlockPopupShown(true)
        localStorage.setItem(getStorageKey(`examUnlockPopupShown_${coursesID}`), "true")
      }

      // อัปเดต allLessonsCompleted ใน userProgress ถ้าจำเป็น
      if (allCompleted !== userProgress.allLessonsCompleted) {
        setUserProgress((prev) => ({
          ...prev,
          allLessonsCompleted: allCompleted,
        }))
      }
    }
  }, [userProgress.completedContents, userId, courses, hasFinalExamState, coursesID, examUnlockPopupShown])

  // ค้นหาเนื้อหาปัจจุบันจาก selectId
  const currentContent = (() => {
    if (!courses) return null

    // ถ้าเป็นข้อสอบท้ายบท
    if (selectId.lessonId === "final" && selectId.contentId === "final-exam") {
      return { id: "final-exam", name: "ข้อสอบท้ายบท", typecontent: "exam" }
    }

    const lesson = courses.lesson?.find((l: any) => l.id === selectId.lessonId)
    return lesson?.content?.find((c: any) => c.id === selectId.contentId)
  })()

  // ค้นหาบทเรียนปัจจุบันจาก selectId
  const currentLesson = (() => {
    if (!courses) return null

    // ถ้าเป็นข้อสอบท้ายบท
    if (selectId.lessonId === "final") {
      return { id: "final", name: "ข้อสอบท้ายบท" }
    }

    return courses.lesson?.find((l: any) => l.id === selectId.lessonId)
  })()

  // ฟังก์ชันสำหรับไปยังเนื้อหาถัดไป
  const goToNextContent = () => {
    if (!courses) return

    // หาบทเรียนปัจจุบัน
    const currentLessonIndex = courses.lesson.findIndex((l: any) => l.id === selectId.lessonId)
    if (currentLessonIndex === -1) return

    const currentLesson = courses.lesson[currentLessonIndex]

    // หาเนื้อหาปัจจุบัน
    const currentContentIndex = currentLesson.content.findIndex((c: any) => c.id === selectId.contentId)
    if (currentContentIndex === -1) return

    // ถ้ายังมีเนื้อหาถัดไปในบทเรียนเดียวกัน
    if (currentContentIndex < currentLesson.content.length - 1) {
      setSelectId({
        lessonId: currentLesson.id,
        contentId: currentLesson.content[currentContentIndex + 1].id,
      })
      return
    }

    // ถ้าไม่มีเนื้อหาถัดไปในบทเรียนเดียวกัน แต่ยังมีบทเรียนถัดไป
    if (currentLessonIndex < courses.lesson.length - 1) {
      const nextLesson = courses.lesson[currentLessonIndex + 1]
      if (nextLesson.content && nextLesson.content.length > 0) {
        setSelectId({
          lessonId: nextLesson.id,
          contentId: nextLesson.content[0].id,
        })
        return
      }
    }

    // ถ้าทำทุกบทเรียนเสร็จแล้ว และมีข้อสอบท้ายบท
    if (userProgress.allLessonsCompleted && hasFinalExamState) {
      setSelectId({
        lessonId: "final",
        contentId: "final-exam",
      })
    }
  }

  // ฟังก์ชันปิดป๊อปอัพ
  const closeUnlockExamPopup = () => {
    setShowUnlockExamPopup(false)
    // บันทึกลง localStorage ว่าเคยแสดงป๊อปอัพแล้ว
    if (typeof window !== "undefined" && userId) {
      localStorage.setItem(getStorageKey(`examUnlockPopupShown_${coursesID}`), "true")
    }
  }

  // ฟังก์ชันไปยังข้อสอบท้ายบท
  const goToFinalExam = () => {
    setSelectId({
      lessonId: "final",
      contentId: "final-exam",
    })
    closeUnlockExamPopup()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  if (!courses) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">ไม่พบคอร์สที่คุณต้องการ</h1>
        <p className="text-gray-600 mb-6">คอร์สนี้อาจถูกลบหรือย้ายไปที่อื่น</p>
        <a
          href="/courses"
          className="px-6 py-2 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] transition-colors"
        >
          กลับไปหน้าคอร์ส
        </a>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <HrawersLearming
        courses={courses}
        select={{
          selectId,
          setSelectId,
        }}
        menuOpen={{
          opened: menuOpen,
          onMenuOpen: setMenuOpen,
        }}
        userProgress={userProgress}
        hasFinalExam={hasFinalExamState}
        userId={userId}
      />

      <LearmingContent
        content={currentContent}
        course={courses}
        lesson={currentLesson}
        menuOpen={{
          opened: menuOpen,
          onMenuOpen: setMenuOpen,
        }}
        selectId={selectId}
        onComplete={handleContentComplete}
        goToNextContent={goToNextContent}
        userId={userId}
      />

      {/* ป๊อปอัพแจ้งเตือนปลดล็อกข้อสอบท้ายบท */}
      {showUnlockExamPopup && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative">
            <button onClick={closeUnlockExamPopup} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600">
              <X size={20} />
            </button>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#E6E9F5] flex items-center justify-center mb-4">
                <CheckCircle size={32} className="text-[#293D97]" />
              </div>

              <h3 className="text-xl font-bold text-gray-900 mb-2">ยินดีด้วย! คุณได้ปลดล็อกข้อสอบท้ายบทแล้ว</h3>
              <p className="text-gray-600 mb-6">
                คุณได้ทำบทเรียนครบทุกบทแล้ว ตอนนี้คุณสามารถทำข้อสอบท้ายบทเพื่อทดสอบความรู้ของคุณได้แล้ว
              </p>

              <div className="flex gap-4">
                <button
                  onClick={closeUnlockExamPopup}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  ทำภายหลัง
                </button>
                <button
                  onClick={goToFinalExam}
                  className="px-4 py-2 bg-[#293D97] text-white rounded-md hover:bg-opacity-90"
                >
                  ทำข้อสอบตอนนี้
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
