"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import dynamic from "next/dynamic"
import UserActivityTracker from "@/components/useWindows"
import type ReactPlayerType from "react-player"
import { getQuizData, type QuizData, type Question } from "@/data/quizQuestion"

// Dynamically import ReactPlayer for client-side rendering
const ReactPlayer = dynamic(() => import("react-player"), { ssr: false })

// เพิ่ม interface สำหรับ props
interface VideoWithQuestionProps {
  videoUrl: string
  questionTime?: number
  quizData?: QuizData
  courseId?: string
  lessonId?: string
  contentId?: string
  userId?: string
  onComplete?: (result: { contentId: string; completed: boolean }) => void
}

const VideoWithQuestion: React.FC<VideoWithQuestionProps> = ({
  videoUrl,
  questionTime = 30,
  quizData,
  courseId,
  lessonId,
  contentId,
  userId = "",
  onComplete,
}) => {
  const [isQuestionOpen, setIsQuestionOpen] = useState(false)
  const [questionAsked, setQuestionAsked] = useState(false)
  const [playing, setPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true) // Start muted for autoplay compliance
  const playerRef = useRef<ReactPlayerType | null>(null)
  const [videoDuration, setVideoDuration] = useState(0)
  const [videoProgress, setVideoProgress] = useState(0)
  const [videoCompleted, setVideoCompleted] = useState(false)
  const [hasQuiz, setHasQuiz] = useState(false)
  const [isReviewMode, setIsReviewMode] = useState(false) // เพิ่มสถานะสำหรับโหมดทบทวน

  // ฟังก์ชันสำหรับสร้าง key ที่ใช้ใน localStorage โดยรวม userId
  const getStorageKey = (key: string) => {
    return `${key}_${userId}`
  }

  // เพิ่ม state สำหรับ quiz
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedChoices, setSelectedChoices] = useState<string[]>([])
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [score, setScore] = useState(0)
  const [totalCorrect, setTotalCorrect] = useState(0)
  const [showResult, setShowResult] = useState(false)
  const [isPassed, setIsPassed] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [loadedQuizData, setLoadedQuizData] = useState<QuizData | null>(null)
  const [askedQuestions, setAskedQuestions] = useState<string[]>([]) // เก็บ ID ของคำถามที่ถามไปแล้ว

  // โหลดข้อมูลคำถามตาม courseId, lessonId และ contentId
  useEffect(() => {
    console.log("Component props:", { courseId, lessonId, contentId, userId })

    if (courseId && lessonId && contentId) {
      console.log("Trying to get quiz data from function")
      const quiz = getQuizData(courseId, lessonId, contentId)
      console.log("Quiz data from function:", quiz)
      setLoadedQuizData(quiz)

      // ตรวจสอบว่ามีควิซหรือไม่
      setHasQuiz(!!quiz && !!quiz.questions && quiz.questions.length > 0)
    } else if (quizData) {
      console.log("Using provided quiz data")
      setLoadedQuizData(quizData)
      setHasQuiz(!!quizData && !!quizData.questions && quizData.questions.length > 0)
    } else {
      setHasQuiz(false)
    }

    // ตรวจสอบว่าเคยดูวิดีโอนี้จบแล้วหรือยัง
    if (contentId && typeof window !== "undefined" && userId) {
      try {
        const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")
        if (completedContents.includes(contentId)) {
          setVideoCompleted(true)
          setIsReviewMode(true)
        }
      } catch (e) {
        console.error("Error reading from localStorage:", e)
      }
    }
  }, [courseId, lessonId, contentId, quizData, userId])

  // เพิ่ม useEffect สำหรับตรวจสอบคำถามตามเวลาในวิดีโอ
  useEffect(() => {
    if (!loadedQuizData || !loadedQuizData.questions || loadedQuizData.questions.length === 0) return

    // เรียงลำดับคำถามตามเวลา
    const sortedQuestions = [...loadedQuizData.questions].filter((q) => q.videoTimestamp !== undefined).sort((a, b) => (a.videoTimestamp! - b.videoTimestamp!))

    if (currentQuestionIndex < sortedQuestions.length) {
      setCurrentQuestion(sortedQuestions[currentQuestionIndex])
    }
  }, [loadedQuizData, currentQuestionIndex])

  useEffect(() => {
    const setVh = () => {
      document.documentElement.style.setProperty("--vh", `${window.innerHeight * 0.01}px`)
    }
    setVh()
    window.addEventListener("resize", setVh)
    return () => window.removeEventListener("resize", setVh)
  }, [])

  useEffect(() => {
    if (isQuestionOpen) {
      document.body.classList.add("modal-open")
    } else {
      document.body.classList.remove("modal-open")
    }
  }, [isQuestionOpen])

  // เพิ่มฟังก์ชันสำหรับย้อนกลับไปยังเวลาที่กำหนด
  const seekToTime = (time: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(time, "seconds")
    }
  }

  // แก้ไขฟังก์ชัน handleSubmitAnswer
  const handleSubmitAnswer = () => {
    if (!currentQuestion || !loadedQuizData) return

    // ตรวจสอบคำตอบที่ถูกต้อง
    const correctChoices = currentQuestion.choices.filter((choice) => choice.isCorrect).map((choice) => choice.id)

    const isCorrect =
      selectedChoices.length === correctChoices.length &&
      selectedChoices.every((id) => correctChoices.includes(id)) &&
      correctChoices.every((id) => selectedChoices.includes(id))

    if (isCorrect) {
      setTotalCorrect((prev) => prev + 1)
      setIsSubmitted(true)

      // หน่วงเวลาก่อนปิด popup เมื่อตอบถูก
      setTimeout(() => {
        handleCloseQuestion()
      }, 2000)
    } else {
      // เมื่อตอบผิด
      setIsSubmitted(true)

      // หาคำถามก่อนหน้า
      const sortedQuestions = [...loadedQuizData.questions].sort((a, b) => (a.videoTimestamp ?? 0) - (b.videoTimestamp ?? 0))
      const currentIndex = sortedQuestions.findIndex((q) => q.id === currentQuestion.id)

      // กำหนดเวลาที่จะย้อนกลับไป
      let seekBackTime = 0 // เริ่มต้นที่ 0 สำหรับคำถามแรก

      if (currentIndex > 0) {
        // ถ้าไม่ใช่คำถามแรก ให้ย้อนกลับไปที่คำถามก่อนหน้า
        seekBackTime = sortedQuestions[currentIndex - 1].videoTimestamp ?? 0
      }

      // แสดงข้อความแจ้งเตือน
      setTimeout(() => {
        setIsQuestionOpen(false)
        setIsSubmitted(false)
        setSelectedChoices([])

        // ลบคำถามนี้ออกจาก askedQuestions เพื่อให้ถามใหม่ได้
        setAskedQuestions((prev) => prev.filter((id) => id !== currentQuestion.id))

        // ย้อนกลับไปเล่นวิดีโอที่จุดเริ่มต้นของเนื้อหาช่วงนั้น
        seekToTime(seekBackTime)
        setPlaying(true)
      }, 2000)
    }
  }

  // เพิ่มฟังก์ชันสำหรับจัดการเมื่อวิดีโอเล่นจบ
  const handleVideoEnded = () => {
    console.log("Video ended")

    // ถ้าไม่มีควิซ ให้มาร์กว่าผ่านเมื่อดูจบ
    if (!hasQuiz && contentId) {
      console.log("No quiz for this video, marking as completed")
      setVideoCompleted(true)

      // บันทึกลง localStorage
      if (typeof window !== "undefined" && userId) {
        try {
          const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")
          if (!completedContents.includes(contentId)) {
            completedContents.push(contentId)
            localStorage.setItem(getStorageKey("completedContents"), JSON.stringify(completedContents))
          }
        } catch (e) {
          console.error("Error writing to localStorage:", e)
        }
      }

      // แจ้ง parent component
      if (onComplete) {
        onComplete({
          contentId: contentId,
          completed: true,
        })
      }

      // แสดงข้อความแจ้งเตือน
      setShowResult(true)
      setIsPassed(true)
    }

    // เพิ่มการแจ้ง parent component ทันทีเมื่อวิดีโอเล่นจบ
    // แจ้ง parent component ทันที
    if (onComplete && contentId) {
      console.log("Video completed, notifying parent")
      onComplete({ contentId: contentId, completed: true })
    }
  }

  // แก้ไขฟังก์ชัน handleProgress เพื่อให้ตรวจสอบคำถามที่ยังไม่ได้ตอบถูก:
  const handleProgress = (state: { playedSeconds: number; played: number; loaded: number; loadedSeconds: number }) => {
    setCurrentTime(state.playedSeconds)
    setVideoProgress(state.played)

    // ถ้าไม่มีข้อมูล quiz หรือไม่มีคำถาม หรืออยู่ในโหมดทบทวน
    if (!loadedQuizData || !loadedQuizData.questions || loadedQuizData.questions.length === 0 || isReviewMode) {
      return
    }

    // ตรวจสอบทุกคำถามว่าถึงเวลาแสดงหรือยัง
    loadedQuizData.questions.forEach((question, index) => {
      // ถ้าถึงเวลาแสดงคำถาม และยังไม่เคยแสดงคำถามนี้ และไม่มีคำถามอื่นกำลังแสดงอยู่
      const shouldShowQuestion =
        state.playedSeconds >= (question.videoTimestamp ?? 0) && !isQuestionOpen && !askedQuestions.includes(question.id)

      if (shouldShowQuestion) {
        setCurrentQuestionIndex(index)
        setCurrentQuestion(question)
        setIsQuestionOpen(true)
        setPlaying(false)
        setSelectedChoices([])
        setIsSubmitted(false)
        setAskedQuestions((prev) => [...prev, question.id]) // เพิ่ม ID คำถามที่แสดงแล้ว
      }
    })
  }

  // เพิ่มฟังก์ชันสำหรับตรวจสอบว่าตอบคำถามครบทุกข้อแล้วหรือไม่
  const checkAllQuestionsAnswered = () => {
    if (!loadedQuizData || !loadedQuizData.questions) return false

    // ตรวจสอบว่าจำนวนคำถามที่ตอบถูกเท่ากับจำนวนคำถามทั้งหมดหรือไม่
    return totalCorrect === loadedQuizData.questions.length
  }

  // เพิ่ม useEffect เพื่อแจ้งเตือนเมื่อตอบคำถามครบทุกข้อ
  useEffect(() => {
    if (loadedQuizData && totalCorrect === loadedQuizData.questions.length && totalCorrect > 0) {
      // ส่งข้อมูลไปยัง parent component ว่าตอบคำถามครบทุกข้อแล้ว
      if (onComplete && contentId) {
        onComplete({
          contentId: contentId,
          completed: true,
        })
      }

      setVideoCompleted(true)
    }
  }, [totalCorrect, loadedQuizData, contentId, onComplete])

  // แก้ไขฟังก์ชัน handleCloseQuestion ให้ไม่ต้องคำนวณเปอร์เซ็นต์ผ่าน
  const handleCloseQuestion = () => {
    setIsQuestionOpen(false)
    setPlaying(true)

    // ถ้าเป็นคำถามสุดท้าย ให้แสดงผลลัพธ์
    if (loadedQuizData && currentQuestionIndex === loadedQuizData.questions.length - 1) {
      setShowResult(true)
      setIsPassed(true) // กำหนดให้ผ่านเสมอเมื่อตอบคำถามครบทุกข้อ
    }
  }

  const handleUnmute = () => {
    setIsMuted(false) // Set muted state to false
  }

  // เพิ่มฟังก์ชันสำหรับเลือกคำตอบ
  const toggleChoiceSelection = (choiceId: string) => {
    if (isSubmitted) return // ไม่ให้เปลี่ยนคำตอบหลังจากส่งคำตอบแล้ว

    setSelectedChoices((prev) => {
      if (prev.includes(choiceId)) {
        return prev.filter((id) => id !== choiceId)
      } else {
        return [...prev, choiceId]
      }
    })
  }

  // เพิ่มฟังก์ชันสำหรับจัดการเมื่อวิดีโอโหลดเสร็จ
  const handleReady = () => {
    setPlaying(true)
    if (playerRef.current) {
      setVideoDuration(playerRef.current.getDuration())
    }
  }

  return (
    <div className="h-screen">
      <div className="relative h-full w-full">
        <ReactPlayer
          ref={playerRef}
          url={videoUrl}
          playing={playing}
          controls={false} // ไม่ให้ผู้ใช้ควบคุมวิดีโอได้
          muted={isMuted} // Controlled mute state
          onProgress={handleProgress}
          onReady={handleReady}
          onEnded={handleVideoEnded}
          width="100%"
          height="100%"
          className="react-player"
          style={{ position: "absolute", top: 0, left: 0 }}
        />

        {/* แสดงสถานะการดูวิดีโอ */}
        {videoCompleted && (
          <div className="absolute top-4 right-4 bg-[#008268] text-white px-3 py-1 rounded-full text-sm font-medium z-10 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            ผ่านแล้ว
          </div>
        )}
      </div>

      <UserActivityTracker onPlay={setPlaying} />

      {isMuted && (
        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-50 z-10">
          <button
            className="bg-white text-gray-800 px-6 py-3 rounded-lg font-medium shadow-lg hover:bg-gray-100 transition-colors"
            onClick={handleUnmute}
          >
            คลิกเพื่อเปิดเสียง
          </button>
        </div>
      )}

      {/* คำถามระหว่างวิดีโอ */}
      {isQuestionOpen && currentQuestion && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 overflow-hidden">
            {/* Header */}
            <div className="bg-[#008268] text-white p-4">
              <div className="flex justify-between items-center">
                <h3 className="font-bold text-lg">
                  คำถามที่ {currentQuestionIndex + 1} จาก {loadedQuizData?.questions.length}
                </h3>
                <div className="text-sm">
                  ตอบถูก {totalCorrect} จาก {askedQuestions.length - 1} ข้อ
                </div>
              </div>
              {/* Progress bar */}
              <div className="w-full bg-white bg-opacity-30 h-2 mt-2 rounded-full overflow-hidden">
                <div
                  className="bg-white h-full rounded-full"
                  style={{ width: `${((currentQuestionIndex + 1) / (loadedQuizData?.questions.length || 1)) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Question content */}
            <div className="p-6">
              <h4 className="text-xl font-medium text-gray-800 mb-4">{currentQuestion.title}</h4>
              {currentQuestion.content && <p className="text-gray-600 mb-4">{currentQuestion.content}</p>}
              {currentQuestion.imageUrl && (
                <div className="mb-4 flex justify-center">
                  <img
                    src={currentQuestion.imageUrl || "/placeholder.svg"}
                    alt="Question"
                    className="max-h-48 object-contain rounded-lg"
                  />
                </div>
              )}

              {/* Choices */}
              <div className="space-y-3 mt-6">
                {currentQuestion.choices.map((choice) => (
                  <div
                    key={choice.id}
                    onClick={() => toggleChoiceSelection(choice.id)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedChoices.includes(choice.id)
                        ? "border-[#008268] bg-[#e6f7f4]"
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    } ${
                      isSubmitted && choice.isCorrect
                        ? "border-green-500 bg-green-50"
                        : isSubmitted && selectedChoices.includes(choice.id) && !choice.isCorrect
                          ? "border-red-500 bg-red-50"
                          : ""
                    }`}
                  >
                    <div className="flex items-center">
                      <div
                        className={`w-6 h-6 flex-shrink-0 rounded-full border ${
                          selectedChoices.includes(choice.id) ? "border-[#008268] bg-[#008268]" : "border-gray-300"
                        } flex items-center justify-center mr-3`}
                      >
                        {selectedChoices.includes(choice.id) && (
                          <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                      </div>
                      <div className="flex-1">
                        {choice.imageUrl ? (
                          <div className="flex flex-col">
                            <img
                              src={choice.imageUrl || "/placeholder.svg"}
                              alt={choice.content || ""}
                              className="h-20 object-contain mb-2"
                            />
                            {choice.content && <span className="text-sm">{choice.content}</span>}
                          </div>
                        ) : (
                          <span>{choice.content || ""}</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 p-4 flex justify-between items-center border-t border-gray-200">
              {isSubmitted ? (
                <div
                  className={`text-lg font-medium ${
                    selectedChoices.every(
                      (id) => currentQuestion.choices.find((c) => c.id === id)?.isCorrect || false,
                    ) && currentQuestion.choices.filter((c) => c.isCorrect).every((c) => selectedChoices.includes(c.id))
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {selectedChoices.every(
                    (id) => currentQuestion.choices.find((c) => c.id === id)?.isCorrect || false,
                  ) && currentQuestion.choices.filter((c) => c.isCorrect).every((c) => selectedChoices.includes(c.id))
                    ? "✓ ถูกต้อง!"
                    : "✗ ไม่ถูกต้อง"}
                </div>
              ) : (
                <div></div>
              )}

              {!isSubmitted ? (
                <button
                  onClick={handleSubmitAnswer}
                  disabled={selectedChoices.length === 0}
                  className={`px-6 py-2 rounded-md font-medium ${
                    selectedChoices.length > 0
                      ? "bg-[#008268] text-white hover:bg-[#006e58]"
                      : "bg-gray-200 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  ส่งคำตอบ
                </button>
              ) : (
                <div className="text-sm text-gray-500">กำลังไปยังคำถามถัดไป...</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* แสดงผลลัพธ์เมื่อตอบคำถามครบทุกข้อหรือดูวิดีโอจบ */}
      {showResult && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 overflow-hidden">
            <div className="p-6 text-center bg-[#E6F7F4]">
              <div className="w-20 h-20 rounded-full mx-auto flex items-center justify-center bg-[#d1efe9] text-[#008268]">
                <svg className="w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mt-4 text-[#008268]">ผ่านแล้ว!</h3>
              {hasQuiz ? (
                <p className="text-gray-600 mt-2">คุณตอบคำถามระหว่างบทเรียนครบทุกข้อแล้ว</p>
              ) : (
                <p className="text-gray-600 mt-2">คุณได้ดูวิดีโอจบแล้ว</p>
              )}
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    setShowResult(false)
                    setPlaying(true)
                  }}
                  className="px-6 py-2 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58]"
                >
                  {hasQuiz ? "ดูวิดีโอต่อ" : "ไปส่วนถัดไป"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default VideoWithQuestion
