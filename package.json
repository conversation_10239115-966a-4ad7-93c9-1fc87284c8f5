{"name": "e-med", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/noto-sans-thai": "^5.2.6", "@fortawesome/fontawesome-free": "^6.7.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/material": "^6.4.8", "@mui/styled-engine-sc": "^6.4.6", "@mui/x-data-grid": "^7.28.0", "@tanstack/react-table": "^8.21.2", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "fabric": "^5.3.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "next": "15.0.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-masonry-css": "^1.0.16", "react-player": "^2.16.0", "styled-components": "^6.1.16", "sweetalert2": "^11.22.1"}, "devDependencies": {"@types/fabric": "^5.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "daisyui": "^4.12.14", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "shadcn-ui": "^0.9.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}