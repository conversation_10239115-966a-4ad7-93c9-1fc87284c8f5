package models_api

type CourseResponse struct {
	Slug              string `json:"slug"`
	CourseName        string `json:"course_name"`
	CoursePicture     string `json:"course_picture"` // base64 encoded
	CourseDescription string `json:"course_description"`
	CourseInstruction string `json:"course_instruction"`
	CourseDifficulty  string `json:"course_difficulty"`
	CourseDuration    string `json:"course_duration"`
	CourseStatus      bool   `json:"course_status"`
	CourseCertificate bool   `json:"course_certificate"`
	LecturerID        uint   `json:"user_id"`
	Lecturer          string `json:"lecturer"`
	LessonAmount      int    `json:"lesson_amount"`
	StudentCount      int    `json:"student_count"`
}
