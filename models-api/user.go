package models_api

type AllUsersRespond struct {
	ID        uint   `json:"id"`
	Picture   string `json:"user_picture"`
	FirstName string `json:"user_fname"`
	LastName  string `json:"user_lname"`
	Email     string `json:"user_email"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Status    string `json:"user_status"`
	Role      string `json:"user_role"`
	Slug      string `json:"user_slug"`
}

type UserRespond struct {
	Picture   string  `json:"user_picture"`
	Password  string  `json:"password"`
	FirstName string  `json:"user_fname"`
	LastName  string  `json:"user_lname"`
	Email     string  `json:"user_email"`
	Position  string  `json:"user_position"`
	LoginDate *string `json:"user_login_date"`
	Role      *string `json:"role"`
	Status    *string `json:"status"`
}

type UserRequest struct {
	Picture   string `json:"user_picture"`
	Password  string `json:"password"`
	FirstName string `json:"user_fname"`
	LastName  string `json:"user_lname"`
	Email     string `json:"user_email"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Role      string `json:"role"`
	Status    string `json:"status"`
}

type CreateUser struct {
	Username  string `json:"username" gorm:"unique" binding:"required"`
	Password  string `json:"password" binding:"required"`
	FirstName string `json:"user_fname" binding:"required"`
	LastName  string `json:"user_lname" binding:"required"`
	Email     string `json:"user_email" gorm:"unique" binding:"email"`
	Position  string `json:"user_position"`
}

type MeResponse struct {
	ID        uint   `json:"id"`
	Username  string `json:"username"`
	FirstName string `json:"user_fname"`
	LastName  string `json:"user_lname"`
	Email     string `json:"user_email"`
	Picture   string `json:"user_picture"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Status    string `json:"user_status"`
	Slug      string `json:"user_slug"`
	Role      string `json:"role"`
}
