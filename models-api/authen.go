package models_api

type SignUpRequest struct {
	Username  string `json:"username" gorm:"unique" binding:"required"`
	Password  string `json:"password" binding:"required"`
	FirstName string `json:"user_fname" binding:"required"`
	LastName  string `json:"user_lname" binding:"required"`
	Email     string `json:"user_email" gorm:"unique" binding:"email"`
}

type LoginRequest struct {
	Username string `json:"username" gorm:"unique" binding:"required"`
	Password string `json:"password" binding:"required"`
}
