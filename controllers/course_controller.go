package controllers

import (
	"encoding/base64"
	"fmt"
	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	models_request "med-api/models-request"
	"med-api/utils"

	"github.com/gofiber/fiber/v2"
)

func CreateCourse(c *fiber.Ctx) error {
	var courseRequest models_request.RequestCourse
	var user models.UserInfo

	if err := c.BodyParser(&courseRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find user by lecturer slug (string)
	if err := db.DB.Where("slug = ?", courseRequest.Lecturer).First(&user).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found."})
	}

	userID := user.ID

	fmt.Println(user.Slug)
	fmt.Println(userID)

	// Handle CoverImage: decode base64 if needed
	var coursePicture []byte
	if courseRequest.CoverImage != nil {
		coursePicture = *courseRequest.CoverImage
	} else {
		base64Str := c.FormValue("coverImage")
		if base64Str != "" {
			decoded, err := base64.StdEncoding.DecodeString(base64Str)
			if err == nil {
				coursePicture = decoded
			}
		}
	}

	newCourse := models.Courses{
		CourseName:        courseRequest.Title,
		CoursePicture:     coursePicture,
		CourseDescription: courseRequest.Description,
		CourseDifficulty:  courseRequest.Difficulty,
		CourseInstruction: courseRequest.ModuleDescription,
		CourseDuration:    fmt.Sprintf("%d", courseRequest.Duration),
		CourseStatus:      courseRequest.Status, // now bool, assign directly
		CourseCertificate: courseRequest.Certify,
		LecturerID:        userID,
		Slug:              utils.GenerateUUIDSlug(),
	}

	// Create course first
	if err := db.DB.Create(&newCourse).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Add lessons if provided
	for _, lessonReq := range courseRequest.Lessons {
		lesson := models.Lessons{
			LessonName:        lessonReq.Name,
			LessonTime:        fmt.Sprintf("%d", lessonReq.Time),
			LessonDescription: lessonReq.Description,
			CourseID:          newCourse.ID,
		}
		if err := db.DB.Create(&lesson).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Failed to create lesson: " + err.Error()})
		}
		// Add lesson content if provided
		for _, contentReq := range lessonReq.Content {
			var contentTypeID uint
			switch contentReq.Type {
			case "text", "Text":
				contentTypeID = 1
			case "video", "Video":
				contentTypeID = 2
			default:
				contentTypeID = 1 // fallback or handle error
			}

			content := models.ContentLesson{
				ContentDescription: contentReq.Details,
				LessonTime:         contentReq.Time,
				LessonID:           lesson.ID,
				ContentLessonType:  contentTypeID,
			}
			if err := db.DB.Create(&content).Error; err != nil {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Failed to create lesson content: " + err.Error()})
			}
		}
	}

	return c.Status(fiber.StatusOK).JSON("Create Success!")
}

func GetCourses(c *fiber.Ctx) error {
	var courses []models.Courses
	if err := db.DB.Preload("Lecture.Role").Find(&courses).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	var response []models_api.CourseResponse
	for _, course := range courses {
		var pictureBase64 string
		if len(course.CoursePicture) > 0 {
			pictureBase64 = base64.StdEncoding.EncodeToString(course.CoursePicture)
		}

		// Count lessons for this course
		var lessonCount int64
		db.DB.Model(&models.Lessons{}).Where("course_id = ?", course.ID).Count(&lessonCount)

		// Count students for this course
		var studentCount int64
		db.DB.Model(&models.CourseStore{}).Where("course_id = ?", course.ID).Count(&studentCount)

		response = append(response, models_api.CourseResponse{
			Slug:              course.Slug,
			CourseName:        course.CourseName,
			CoursePicture:     pictureBase64,
			CourseDescription: course.CourseDescription,
			CourseInstruction: course.CourseInstruction,
			CourseDifficulty:  course.CourseDifficulty,
			CourseDuration:    course.CourseDuration,
			CourseStatus:      course.CourseStatus,
			CourseCertificate: course.CourseCertificate,
			LecturerID:        course.LecturerID,
			Lecturer:          course.Lecture.FirstName + " " + course.Lecture.LastName,
			LessonAmount:      int(lessonCount),
			StudentCount:      int(studentCount),
		})
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

func GetCourseID(c *fiber.Ctx) error {
	return nil
}

func UpdateCourse(c *fiber.Ctx) error {
	return nil
}

func DeleteCourse(c *fiber.Ctx) error {
	return nil
}
