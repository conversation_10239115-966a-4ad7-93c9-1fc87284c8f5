package models_request

type RequestContent struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"typecontent"` // "text" | "video"
	Details string `json:"details"`
	Time    int    `json:"time"` // seconds
}

type Lesson struct {
	ID          string           `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Time        int              `json:"time"` // seconds
	Content     []RequestContent `json:"content"`
}

// type RequestCourse struct {
// 	CourseName        string `json:"course_name" binding:"required"`
// 	LecturerSlug      uint   `json:"lecturer_slug" binding:"required"`
// 	CoursePicture     string `json:"course_picture"`
// 	CourseDescription string `json:"course_description"`
// 	CourseInstruction string `json:"course_module"`
// 	CourseDifficulty  string `json:"course_difficulty"`
// 	CourseDuration    string `json:"course_duration"`
// 	CourseStatus      string `json:"course_status"`
// 	CourseCertificate bool   `json:"course_certificate"`
// }

type RequestCourse struct {
	Title             string   `json:"title" binding:"required"`
	Lecturer          string   `json:"lecturer" binding:"required"`
	Description       string   `json:"description"`
	ModuleDescription string   `json:"moduleDescription"`
	Difficulty        string   `json:"difficulty"`
	Duration          int      `json:"duration"` // hours
	Status            bool     `json:"status"`
	CoverImage        *[]byte  `json:"coverImage"` // store as binary for longblob
	Certify           bool     `json:"certify"`
	Lessons           []Lesson `json:"lessons"`
}
