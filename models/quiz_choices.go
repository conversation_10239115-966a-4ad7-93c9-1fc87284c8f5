package models

type QuizChoices struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	Choice    string `json:"quiz_choice" binding:"required"`
	IsCorrect bool   `json:"is_correct" binding:"required"`

	QuizChoiceType uint        `json:"quiz_choice_type"`
	ChoiceType     ContentType `json:"choice_type" gorm:"foreignKey:QuizChoiceType"`

	QuizQuestionID uint     `json:"quiz_question_id"`
	QuizQuestion   Question `json:"quiz_question" gorm:"foreignKey:QuizQuestionID"`
}
