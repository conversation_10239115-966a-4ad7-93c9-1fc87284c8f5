package models

type UserInfo struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	Picture   string `json:"user_picture"`
	Username  string `json:"username" gorm:"unique" binding:"required"`
	Password  string `json:"password" binding:"required"`
	FirstName string `json:"user_fname" binding:"required"`
	LastName  string `json:"user_lname" binding:"required"`
	Email     string `json:"user_email" gorm:"unique" binding:"email"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Status    string `json:"user_status"`
	Slug      string `json:"user_slug"`

	RoleID uint `json:"role_id"`
	Role   Role `json:"role" gorm:"foreignKey:RoleID"`
}
