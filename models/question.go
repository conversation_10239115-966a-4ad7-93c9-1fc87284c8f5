package models

type Question struct {
	ID         uint   `json:"id" gorm:"primaryKey"`
	Title      string `json:"title" binding:"required"`
	Detail     string `json:"question_detail" binding:"required"`
	TimeInsert int    `json:"time_insert" binding:"required"`

	QuizID uint `json:"quiz_id"`
	Quiz   Quiz `json:"quiz" gorm:"foreignKey:QuizID"`

	QuizType uint        `json:"quiz_type"`
	TypeQuiz ContentType `json:"type_quiz" gorm:"foreignKey:QuizType"`
}
