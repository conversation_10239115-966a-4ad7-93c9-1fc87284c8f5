package models

type ContentLesson struct {
	ID                 uint   `json:"id" gorm:"primaryKey"`
	ContentDescription string `json:"content_lesson_description"`
	LessonTime         int    `json:"lesson_time"`

	ContentLessonType uint        `json:"content_lesson_type"`
	ContentType       ContentType `json:"content_type" gorm:"foreignKey:ContentLessonType"`

	LessonID uint    `json:"lesson_id"`
	Lesson   Lessons `json:"lesson" gorm:"foreignKey:LessonID"`
}
