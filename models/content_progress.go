package models

type ContentProgress struct {
	ID           uint   `json:"id" gorm:"primaryKey"`
	CourseRecord string `json:"course_progress_record"`
	LessonRecord string `json:"lesson_progress_record"`

	ContentLessonID uint          `json:"content_lesson_id"`
	ContentLesson   ContentLesson `json:"content_lesson" gorm:"foreignKey:ContentLessonID"`

	UserID uint     `json:"user_id"`
	User   UserInfo `json:"user" gorm:"foreignKey:UserID"`
}
